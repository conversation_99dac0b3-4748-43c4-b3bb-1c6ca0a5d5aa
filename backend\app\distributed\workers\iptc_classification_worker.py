"""
IPTC Classification worker for distributed ETL processing.

This module provides the IPTCClassificationWorker class that handles classifying
news articles into IPTC NewsCodes using Hugging Face models in batch processing mode.
"""

import logging
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from backend.app.distributed.distributed_worker import Distri<PERSON><PERSON>orker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class IPTCClassificationWorker(DistributedWorker):
    """
    Worker specialized in classifying news articles into IPTC NewsCodes.
    
    Adapts the existing classify_iptc_newscode logic for batch processing with
    batch-optimized Hugging Face model processing, proper resource management,
    and error handling.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the IPTC classification worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.IPTC_CLASSIFICATION not in config.stages:
            raise ValueError("IPTCClassificationWorker requires IPTC_CLASSIFICATION stage")
        
        # Initialize classification model (lazy loading)
        self._classifier = None
        
        # Configuration for IPTC classification
        self.model_name = config.get_stage_config(
            ProcessingStage.IPTC_CLASSIFICATION, 'model_name', 
            'classla/multilingual-IPTC-news-topic-classifier'
        )

        self.preload_model = config.get_stage_config(
            ProcessingStage.IPTC_CLASSIFICATION, 'preload_model', False
        )
        self.max_text_length = config.get_stage_config(
            ProcessingStage.IPTC_CLASSIFICATION, 'max_text_length', None
        )
        
        logger.info(
            f"Initialized IPTCClassificationWorker {self.worker_id} "
            f"(model: {self.model_name}, preload: {self.preload_model})"
        )
        
        # Preload model if configured
        if self.preload_model:
            self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the Hugging Face classification model."""
        try:
            logger.info(f"Initializing IPTC classification model: {self.model_name}")
            
            from transformers import pipeline
            self._classifier = pipeline(
                "text-classification", 
                model=self.model_name
            )
            
            # Get max length from model config if not explicitly set
            if self.max_text_length is None:
                try:
                    self.max_text_length = self._classifier.model.config.max_position_embeddings
                    logger.info(f"Using model max_position_embeddings: {self.max_text_length}")
                except AttributeError:
                    # Fallback to a reasonable default
                    self.max_text_length = 512
                    logger.warning(f"Could not get max_position_embeddings, using default: {self.max_text_length}")
            
            logger.info(f"IPTC classification model {self.model_name} loaded successfully")
            
        except ImportError as e:
            logger.error(f"Failed to import transformers library: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize IPTC classification model {self.model_name}: {e}")
            raise
    
    def _get_classifier(self):
        """Get classifier model, initializing if needed."""
        if self._classifier is None:
            from transformers import pipeline
            self._classifier = pipeline(
                "text-classification", 
                model=self.model_name
            )
            
            # Get max length from model config if not explicitly set
            if self.max_text_length is None:
                try:
                    self.max_text_length = self._classifier.model.config.max_position_embeddings
                except AttributeError:
                    self.max_text_length = 512
            
            logger.debug(f"Lazy-loaded IPTC classification model: {self.model_name}")
        return self._classifier
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for IPTC classification.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be IPTC_CLASSIFICATION)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.IPTC_CLASSIFICATION:
            logger.error(f"IPTCClassificationWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for IPTC classification")
        
        # Filter entries that have required text fields
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.full_text:
                logger.warning(
                    f"Entry {entry.entry_id} missing required full_text field"
                )
                results[entry.entry_id] = False
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.warning("No valid entries to process in batch")
            return results
        
        # Process valid entries using batch processing for optimal performance
        batch_results = self._process_batch(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed IPTC classification batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_batch(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries using batch processing for optimal performance.

        Args:
            entries: List of valid entries to process

        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        try:
            # Get classifier once for the entire batch
            classifier = self._get_classifier()
            
            # Prepare batch data
            texts = []
            entry_ids = []
            
            for entry in entries:
                # Combine title, description, and full_text as in original implementation
                input_text = f"{entry.title or ''}\n{entry.description or ''}\n{entry.full_text or ''}"
                
                # Truncate text if too long
                if len(input_text) > self.max_text_length:
                    input_text = input_text[:self.max_text_length]
                    logger.debug(f"Truncated text for entry {entry.entry_id} to {self.max_text_length} characters")
                
                texts.append(input_text)
                entry_ids.append(entry.entry_id)
            
            # Batch classify texts
            logger.debug(f"Classifying {len(texts)} texts in batch")
            classifications = classifier(texts)
            
            # Handle both single result and batch result formats
            if not isinstance(classifications[0], list):
                # Single results format - wrap each result in a list
                classifications = [[result] for result in classifications]
            
            # Update database for each entry
            for i, (entry_id, classification_results) in enumerate(zip(entry_ids, classifications)):
                try:
                    # Take the top classification result
                    top_result = classification_results[0]
                    iptc_newscode = top_result['label']
                    iptc_score = top_result['score']
                    
                    success = self._update_entry_classification(entry_id, iptc_newscode, iptc_score)
                    results[entry_id] = success
                    
                    if success:
                        logger.debug(f"Successfully classified entry {entry_id}: {iptc_newscode} (score: {iptc_score:.3f})")
                    else:
                        logger.warning(f"Failed to update database for entry {entry_id}")
                
                except Exception as e:
                    logger.error(f"Error updating entry {entry_id}: {e}")
                    results[entry_id] = False
        
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            # Mark all entries as failed
            for entry in entries:
                results[entry.entry_id] = False
        
        return results
    
    def _process_batch_individual(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries individually (fallback method).
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        # Get classifier
        classifier = self._get_classifier()
        
        for entry in entries:
            try:
                # Combine title, description, and full_text as in original implementation
                input_text = f"{entry.title or ''}\n{entry.description or ''}\n{entry.full_text or ''}"
                
                # Truncate text if too long
                if len(input_text) > self.max_text_length:
                    input_text = input_text[:self.max_text_length]
                    logger.debug(f"Truncated text for entry {entry.entry_id} to {self.max_text_length} characters")
                
                # Classify text (individual)
                classification_result = classifier(input_text)
                
                # Handle both single result and list result formats
                if isinstance(classification_result, list):
                    top_result = classification_result[0]
                else:
                    top_result = classification_result
                
                iptc_newscode = top_result['label']
                iptc_score = top_result['score']
                
                # Update database
                success = self._update_entry_classification(entry.entry_id, iptc_newscode, iptc_score)
                results[entry.entry_id] = success
                
                if success:
                    logger.debug(f"Successfully classified entry {entry.entry_id}: {iptc_newscode} (score: {iptc_score:.3f})")
                else:
                    logger.warning(f"Failed to update database for entry {entry.entry_id}")
            
            except Exception as e:
                logger.error(f"Error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _update_entry_classification(self, entry_id: str, iptc_newscode: str, iptc_score: float) -> bool:
        """
        Update the entry's IPTC classification fields in the database.
        
        Args:
            entry_id: ID of the entry to update
            iptc_newscode: Classified IPTC news code
            iptc_score: Classification confidence score
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Update IPTC classification fields
                db_session.query(Entry).filter_by(entry_id=entry_id).update({
                    "iptc_newscode": iptc_newscode,
                    "iptc_score": iptc_score
                })
                db_session.commit()
                
                logger.debug(f"Updated IPTC classification for entry {entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry_id}: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to IPTC classification worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add IPTC classification worker specific metrics
        health.update({
            'worker_type': 'IPTCClassificationWorker',
            'model_name': self.model_name,
            'batch_optimize_model': self.batch_optimize_model,
            'preload_model': self.preload_model,
            'max_text_length': self.max_text_length,
            'model_loaded': self._classifier is not None,
            'supported_stages': [ProcessingStage.IPTC_CLASSIFICATION.value]
        })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up model resources when worker shuts down."""
        logger.info("Cleaning up IPTC classification model resources")
        
        # Clear model reference to free memory
        self._classifier = None
        
        logger.info("IPTC classification model resources cleaned up")