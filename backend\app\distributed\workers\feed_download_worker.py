"""
Feed download worker for distributed ETL processing.

This module provides the FeedDownloadWorker class that handles downloading
and parsing RSS feeds to extract news entries in batch processing mode.
"""

import logging
import feedparser
import hashlib
import pandas as pd
import yaml
import os
from datetime import datetime, timezone
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
import re

from sqlalchemy.orm import Session
from sqlalchemy import text

from backend.app.distributed.distributed_worker import Distri<PERSON><PERSON>orker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry
from backend.app.core.config import settings

logger = logging.getLogger(__name__)


class FeedDownloadWorker(DistributedWorker):
    """
    Worker specialized in downloading and parsing RSS feeds.
    
    Adapts the existing download_feeds logic for batch processing with
    proper error handling, retry logic, and progress tracking.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the feed download worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.DOWNLOAD_FEEDS not in config.stages:
            raise ValueError("FeedDownloadWorker requires DOWNLOAD_FEEDS stage")
        
        # Configuration for feed operations
        self.feed_timeout = config.get_stage_config(
            ProcessingStage.DOWNLOAD_FEEDS, 'feed_timeout', 30
        )
        self.max_concurrent_feeds = config.get_stage_config(
            ProcessingStage.DOWNLOAD_FEEDS, 'max_concurrent_feeds', 3
        )
        
        # Load RSS sources configuration
        self.rss_sources = self._load_rss_sources()
        
        # Clean up old temp tables on startup
        self._cleanup_old_temp_tables()

        logger.info(
            f"Initialized FeedDownloadWorker {self.worker_id} "
            f"(timeout: {self.feed_timeout}s, "
            f"max_concurrent: {self.max_concurrent_feeds}, "
            f"sources: {len(self.rss_sources)})"
        )
    
    def _load_rss_sources(self) -> List[Dict[str, Any]]:
        """Load RSS sources from configuration file."""
        rss_file_path = settings.NEWS_RSS_SOURCES_FILE
        if not os.path.isabs(rss_file_path):
            rss_file_path = os.path.join('etl', rss_file_path)
        
        try:
            with open(rss_file_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                return config['rss_sources']
        except Exception as e:
            logger.error(f"Failed to load RSS sources from {rss_file_path}: {e}")
            return []

    def _cleanup_old_temp_tables(self) -> None:
        """Clean up old temp_entries tables (older than 10 minutes)."""
        try:
            db_session = self.db_session_factory()
            try:
                from datetime import datetime, timezone, timedelta
                from sqlalchemy import text

                # Calculate cutoff time (10 minutes ago)
                cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=10)
                cutoff_string = cutoff_time.strftime("%Y%m%d_%H%M%S")

                # Query for temp tables older than cutoff
                # Oracle syntax to find tables matching pattern (updated for new naming scheme)
                query = text("""
                    SELECT table_name
                    FROM user_tables
                    WHERE table_name LIKE 'TEMP_ENTRIES_%'
                    AND SUBSTR(table_name, 14, 15) < :cutoff_string
                """)

                result = db_session.execute(query, {"cutoff_string": cutoff_string})
                old_tables = [row[0] for row in result.fetchall()]

                # Drop old temp tables
                dropped_count = 0
                for table_name in old_tables:
                    try:
                        drop_query = text(f"DROP TABLE {table_name} PURGE")
                        db_session.execute(drop_query)
                        dropped_count += 1
                        logger.debug(f"Dropped old temp table: {table_name}")
                    except Exception as e:
                        logger.warning(f"Failed to drop temp table {table_name}: {e}")

                db_session.commit()

                if dropped_count > 0:
                    logger.info(f"[download_feeds] Cleaned up {dropped_count} old temp tables")
                else:
                    logger.debug(f"[download_feeds] No old temp tables to clean up")

            except Exception as e:
                db_session.rollback()
                logger.error(f"[download_feeds] Error during temp table cleanup: {e}")
            finally:
                db_session.close()

        except Exception as e:
            logger.error(f"[download_feeds] Failed to create database session for cleanup: {e}")
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of RSS sources for feed download.
        
        Note: For feed download, we don't process existing entries but rather
        download from RSS sources. The entries parameter is ignored.
        
        Args:
            entries: List of Entry objects (ignored for feed download)
            stage: Processing stage (should be DOWNLOAD_FEEDS)
            
        Returns:
            Dictionary mapping source URL to success status (True/False)
        """
        if stage != ProcessingStage.DOWNLOAD_FEEDS:
            logger.error(f"FeedDownloadWorker cannot process stage {stage.value}")
            return {}
        
        logger.info(f"Processing {len(self.rss_sources)} RSS sources for feed download")
        
        results = {}
        
        # Process RSS sources concurrently
        with ThreadPoolExecutor(max_workers=self.max_concurrent_feeds) as executor:
            # Submit all feed download tasks
            future_to_source = {
                executor.submit(self._download_and_process_feed, source): source
                for source in self.rss_sources
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_source):
                source = future_to_source[future]
                
                try:
                    entries_processed = future.result()
                    
                    if entries_processed is not None:
                        results[source['url']] = True
                        logger.info(f"[download_feeds] Successfully processed {entries_processed} entries from {source['url']}")
                    else:
                        results[source['url']] = False
                        logger.warning(f"[download_feeds] Failed to process feed from {source['url']}")
                
                except Exception as e:
                    logger.error(f"Error processing RSS source {source['url']}: {e}")
                    results[source['url']] = False
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"[download_feeds] Completed feed processing: {success_count}/{len(self.rss_sources)} successful feeds"
        )
        
        return results
    
    def _download_and_process_feed(self, source: Dict[str, Any]) -> Optional[int]:
        """
        Download and process a single RSS feed.
        
        Args:
            source: RSS source configuration dictionary
            
        Returns:
            Number of entries processed or None if failed
        """
        try:
            logger.debug(f"Downloading RSS feed from {source['url']}")
            
            # Parse RSS feed
            feed = feedparser.parse(source['url'])
            
            if feed.bozo:
                logger.warning(f"RSS feed parsing error for {source['url']}: {feed.bozo_exception}")
                return None
            
            # Extract entries from feed
            entries = []
            for entry in feed.entries:
                # Only set description if it exists and has content
                description = getattr(entry, 'description', None)
                cleaned_description = self._clean_html_content(description) if description else None
                
                # Ensure empty descriptions become NULL
                if cleaned_description and cleaned_description.strip():
                    final_description = cleaned_description
                else:
                    final_description = None
                
                entries.append({
                    'entry_id': self._generate_entry_id(entry, source),
                    'title': entry.title,
                    'link': entry.link,
                    'source': source['url'],
                    'description': final_description,
                    'published': datetime(*entry.published_parsed[:6], tzinfo=timezone.utc)
                        if hasattr(entry, 'published_parsed') and entry.published_parsed else None,
                })
            
            if not entries:
                logger.info(f"No entries found in RSS feed {source['url']}")
                return 0
            
            # Convert to DataFrame and remove duplicates
            df = pd.DataFrame(entries).drop_duplicates(subset=['entry_id'])
            
            # Preprocess entries based on source
            df = self._preprocess_entries(df, source)
            
            # Save entries to database
            saved_count = self._save_entries(df)
            
            logger.debug(f"Processed {len(df)} entries from {source['url']}, saved {saved_count}")
            return saved_count
        
        except Exception as e:
            logger.error(f"Error downloading RSS feed {source['url']}: {e}")
            return None
    
    def _generate_entry_id(self, entry: Any, source_config: Dict[str, Any]) -> str:
        """Generate unique ID from the fields defined in entry_id_from."""
        fields = source_config['entry_id_from'].split(',')
        id_parts = [str(getattr(entry, field.strip(), '')) for field in fields]
        return hashlib.sha256('-'.join(id_parts).encode()).hexdigest()
    
    def _clean_html_content(self, html_content: str) -> str:
        """Convert HTML content to clean text with proper formatting."""
        if not html_content:
            return ""
        
        # Parse HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Replace <br>, <p>, </p>, </div> tags with newlines
        for tag in soup.find_all(['br', 'p', 'div']):
            tag.replace_with('\n' + tag.get_text() + '\n')
        
        # Get text and clean up
        text = soup.get_text()
        
        # Remove excessive whitespace/newlines while preserving paragraph structure
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Replace multiple newlines with double newline
        text = re.sub(r' +', ' ', text)  # Replace multiple spaces with single space
        text = text.strip()  # Remove leading/trailing whitespace
        
        return text
    
    def _preprocess_entries(self, df: pd.DataFrame, source: Dict[str, Any]) -> pd.DataFrame:
        """Preprocess entries depending on source."""
        if source['clean_name'] == "Golem":
            # Remove all entries that contain "Anzeige:" in the title as well as Golem+ articles
            df = df[~df['title'].str.contains(r"Anzeige:|\(g\+\)", na=False)]
        elif source['clean_name'] == "Stiftung Warentest":
            # Remove all entries that contain financial terms in the title
            df = df[~df['title'].str.contains(
                "Tagesgeld|Festgeld|Zins|anlegen|Gasanbieter|Stromanbieter", na=False
            )]
        
        return df
    
    def _save_entries(self, df: pd.DataFrame) -> int:
        """
        Save entries to database, ignoring duplicates.
        
        Args:
            df: DataFrame containing entries to save
            
        Returns:
            Number of entries actually saved
        """
        if df.empty:
            return 0
        
        try:
            # Create database session
            db_session = self.db_session_factory()
            
            try:
                # Create temporary table name with timestamp, microseconds, and thread ID to avoid race conditions
                import threading
                import uuid
                datetime_string = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S_%f")  # Full microseconds
                thread_id = threading.current_thread().ident
                unique_suffix = str(uuid.uuid4())[:8]  # Short unique identifier
                temp_table = f"temp_entries_{datetime_string}_{thread_id}_{unique_suffix}"
                
                # Prepare merge columns and values
                merge_columns = ', '.join(df.columns)
                merge_values = ', '.join(f't.{col}' for col in df.columns)
                
                # SQL for merging data
                merge_sql = f"""
                    MERGE INTO entries e
                    USING {temp_table} t
                    ON (e.entry_id = t.entry_id)
                    WHEN NOT MATCHED THEN
                    INSERT ({merge_columns})
                    VALUES ({merge_values})
                """
                
                # Get column types from Entry model
                dtype_dict = {
                    column.name: column.type 
                    for column in Entry.__table__.columns 
                    if column.name in df.columns
                }
                
                # Write DataFrame to temp table
                df.to_sql(
                    temp_table,
                    db_session.bind,
                    if_exists='replace',
                    index=False,
                    dtype=dtype_dict,
                    chunksize=1000
                )
                
                # Merge into main table
                result = db_session.execute(text(merge_sql))
                rowcount = result.rowcount
                
                # Drop temp table
                db_session.execute(text(f"DROP TABLE {temp_table} PURGE"))
                
                db_session.commit()
                
                logger.debug(f"Inserted {rowcount} of {len(df)} entries")
                return rowcount
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error saving entries: {e}")
                return 0
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for saving entries: {e}")
            return 0
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to feed download worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add feed download specific metrics
        health.update({
            'worker_type': 'FeedDownloadWorker',
            'feed_timeout': self.feed_timeout,
            'max_concurrent_feeds': self.max_concurrent_feeds,
            'rss_sources_count': len(self.rss_sources),
            'supported_stages': [ProcessingStage.DOWNLOAD_FEEDS.value]
        })
        
        return health