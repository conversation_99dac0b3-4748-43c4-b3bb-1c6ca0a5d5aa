"""
Parallel Distributed Worker - One thread per stage architecture.

This module implements the correct parallel worker architecture matching the monolithic ETL:
- ONE thread per stage (not multiple workers per stage)
- Each thread processes its assigned stage independently
- Each thread only updates processing_status for its own stage
- Threads run continuously like in the monolithic ETL

This matches the monolithic pattern:
threading.Thread(target=aggregator.download_full_texts, name="DownloadFullTextsThread").start()
threading.Thread(target=aggregator.llm_sentiment_ad, name="LLMSentimentAdThread").start()
threading.Thread(target=aggregator.classify_iptc_newscode, name="ClassifyIPTCNewsCodeThread").start()
"""

import logging
import threading
import time
import json
from contextlib import contextmanager
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Set

from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatusValue, ProcessingStatus
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.worker_factory import WorkerFactory
from backend.app.core.database import SessionLocal
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class OneThreadPerStageWorker:
    """
    One thread per stage worker - matches monolithic ETL architecture.

    This creates exactly ONE thread per stage, just like the monolithic ETL:
    - Each thread continuously processes its assigned stage
    - Each thread only updates processing_status for its own stage
    - No interference between stages
    - Matches the proven monolithic pattern
    """

    def __init__(self, stages: List[ProcessingStage], worker_id: Optional[str] = None):
        self.stages = stages
        # Generate worker ID with timestamp including milliseconds
        if worker_id is None:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
            worker_id = f"stage-threads-{timestamp}"
        self.worker_id = worker_id
        self.shutdown_event = threading.Event()
        self.stage_threads: Dict[ProcessingStage, threading.Thread] = {}
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.db_session_factory = SessionLocal
        self.work_queue_manager = WorkQueueManager(self.db_session_factory)

        # Health monitoring and progress reporting
        self._health_manager = None
        self._progress_reporter = None
        self._recovery_system = None

        # Create specialized workers for each stage
        self.stage_workers: Dict[ProcessingStage, object] = {}
        for stage in stages:
            try:
                # Create a simple config for this stage
                from backend.app.distributed.worker_config import WorkerConfig
                config = WorkerConfig(
                    worker_id=f"{self.worker_id}-{stage.value}",
                    stages=[stage]
                )
                worker = WorkerFactory.create_worker(config, self.db_session_factory)
                self.stage_workers[stage] = worker
                logger.info(f"Created specialized worker for stage {stage.value}: {type(worker).__name__}")
            except Exception as e:
                logger.error(f"Failed to create worker for stage {stage.value}: {e}")
                # Continue without this stage worker - will fall back to simulation

        # Performance tracking per stage
        self.stage_stats = {
            stage: {
                'batches_processed': 0,
                'entries_processed': 0,
                'errors': 0,
                'last_activity': None,
                'thread_alive': False
            }
            for stage in stages
        }

        logger.info(f"Initialized one-thread-per-stage worker {self.worker_id} for stages: {[s.value for s in stages]}")

    def start(self):
        """Start one thread for each stage - matches monolithic ETL pattern."""
        logger.info(f"Starting {len(self.stages)} stage threads (one per stage)")

        # Register worker with health monitoring system
        self._register_worker()

        # Start heartbeat thread
        self._start_heartbeat_thread()

        # Start recovery system for worker timeout management
        self._start_recovery_system()

        for stage in self.stages:
            # Create and start thread for this stage (like monolithic ETL)
            thread = threading.Thread(
                target=self._stage_thread_loop,
                args=(stage,),
                name=f"{stage.value}Thread",  # Match monolithic naming: "DownloadFullTextsThread"
                daemon=True
            )

            thread.start()
            self.stage_threads[stage] = thread
            self.stage_stats[stage]['thread_alive'] = True

            logger.info(f"Started {stage.value}Thread (matches monolithic ETL pattern)")

        logger.info(f"All {len(self.stage_threads)} stage threads started successfully")
    
    def _stage_thread_loop(self, stage: ProcessingStage):
        """
        Stage thread loop - matches monolithic ETL pattern.

        This is the core loop for each stage thread. It:
        1. Claims work for this stage only
        2. Processes the batch
        3. Updates processing_status ONLY for this stage (no interference)
        4. Continues indefinitely like monolithic ETL threads
        """
        thread_name = threading.current_thread().name
        logger.info(f"Stage thread {thread_name} started for {stage.value}")

        consecutive_no_work = 0
        batch_size = 10  # Default batch size
        max_retries = 3

        while not self.shutdown_event.is_set():
            try:
                batch_start_time = time.time()

                # Create isolated database session for this stage thread
                with self._get_stage_session() as session:
                    work_queue = WorkQueueManager(session)

                    # Check if this is a singleton stage (like download_feeds)
                    from backend.app.distributed.processing_stage import stage_requires_singleton

                    if stage_requires_singleton(stage):
                        # Handle singleton stages differently - no entry retrieval
                        work_processed = self._process_singleton_stage(work_queue, stage)
                        if not work_processed:
                            consecutive_no_work += 1
                            delay = 5.0 if consecutive_no_work < 10 else 10.0  # Adaptive delay

                            if consecutive_no_work % 20 == 0:  # Log every 20 cycles
                                logger.debug(f"Singleton stage {stage.value}: No work available ({consecutive_no_work} cycles)")

                            if not self.shutdown_event.wait(delay):
                                continue
                            else:
                                break
                        else:
                            # Work was processed, reset counter
                            consecutive_no_work = 0
                            # Continue to next iteration without delay for singleton stages
                            continue
                    else:
                        # Regular entry-based processing for non-singleton stages
                        entry_ids = work_queue.claim_batch(
                            stage=stage,
                            batch_size=batch_size,
                            worker_id=f"{self.worker_id}-{stage.value}",
                            max_retries=max_retries
                        )

                        if not entry_ids:
                            # No work available for this stage
                            consecutive_no_work += 1
                            delay = 5.0 if consecutive_no_work < 10 else 10.0  # Adaptive delay

                            if consecutive_no_work % 20 == 0:  # Log every 20 cycles
                                logger.debug(f"Stage {stage.value}: No work available ({consecutive_no_work} cycles)")

                            if not self.shutdown_event.wait(delay):
                                continue
                            else:
                                break

                    # Work found - reset no-work counter
                    consecutive_no_work = 0

                    # Load entries for processing
                    entries = session.query(Entry).filter(Entry.entry_id.in_(entry_ids)).all()

                    if not entries:
                        logger.warning(f"Stage {stage.value}: Claimed {len(entry_ids)} entries but found none in database")
                        continue

                    logger.info(f"Stage {stage.value}: Processing batch of {len(entries)} entries")

                    # Process the batch (this would call the actual processing logic)
                    success_count = self._process_stage_batch(session, entries, stage)

                    # Update statistics
                    self.stage_stats[stage]['batches_processed'] += 1
                    self.stage_stats[stage]['entries_processed'] += success_count
                    self.stage_stats[stage]['last_activity'] = datetime.now(timezone.utc)

                    batch_duration = time.time() - batch_start_time
                    logger.info(f"Stage {stage.value}: Processed {success_count}/{len(entries)} entries in {batch_duration:.2f}s")

                    # Update progress in health monitoring system
                    self._update_worker_progress(stage, success_count, len(entries))

                    # Short delay between batches
                    if not self.shutdown_event.wait(1.0):
                        continue
                    else:
                        break

            except Exception as e:
                self.stage_stats[stage]['errors'] += 1
                logger.error(f"Error in stage {stage.value} thread: {e}", exc_info=True)

                # Error recovery delay
                if not self.shutdown_event.wait(5.0):
                    continue
                else:
                    break

        self.stage_stats[stage]['thread_alive'] = False
        logger.info(f"Stage thread {thread_name} for {stage.value} shutting down")

    def _process_stage_batch(self, session, entries: List[Entry], stage: ProcessingStage) -> int:
        """
        Process a batch of entries for the given stage.

        This method updates ONLY the processing_status for this stage,
        without interfering with other stages' status.

        Returns:
            Number of successfully processed entries
        """
        success_count = 0

        worker_id = f"{self.worker_id}-{stage.value}"

        for entry in entries:
            try:
                # Load current processing status using the proper ProcessingStatus class
                processing_status = ProcessingStatus.from_json(entry.processing_status)

                # Mark this stage as in progress (only this stage)
                processing_status.mark_stage_in_progress(stage, worker_id)
                entry.processing_status = processing_status.to_json()
                session.commit()

                # Call actual processing logic using specialized workers
                processed_successfully = self._process_entry_with_specialized_worker(entry, stage)

                if processed_successfully:
                    # Mark this stage as completed
                    processing_status.mark_stage_completed(stage, worker_id)
                    success_count += 1
                else:
                    # Mark this stage as failed
                    processing_status.mark_stage_failed(stage, worker_id, "Processing simulation failed")

                # Update processing status (only this stage affected)
                entry.processing_status = processing_status.to_json()
                session.commit()

            except Exception as e:
                logger.error(f"Error processing entry {entry.entry_id} for stage {stage.value}: {e}")

                # Mark as failed using proper ProcessingStatus class
                try:
                    processing_status = ProcessingStatus.from_json(entry.processing_status)
                    processing_status.mark_stage_failed(stage, worker_id, str(e))
                    entry.processing_status = processing_status.to_json()
                    session.commit()
                except Exception as commit_error:
                    logger.error(f"Error updating failed status: {commit_error}")

        return success_count

    def _process_entry_with_specialized_worker(self, entry: Entry, stage: ProcessingStage) -> bool:
        """
        Process entry using the specialized worker for this stage.

        Args:
            entry: Entry to process
            stage: Processing stage

        Returns:
            True if processing was successful, False otherwise
        """
        # Get the specialized worker for this stage
        stage_worker = self.stage_workers.get(stage)

        if not stage_worker:
            logger.warning(f"No specialized worker available for stage {stage.value}, falling back to simulation")
            # Fallback to simulation for stages without specialized workers
            import random
            return random.random() > 0.1

        try:
            # Use the specialized worker to process a batch of one entry
            results = stage_worker.process_batch([entry], stage)

            # Get the result for this entry
            success = results.get(entry.entry_id, False)

            if success:
                logger.debug(f"Successfully processed entry {entry.entry_id} with {type(stage_worker).__name__}")
            else:
                logger.warning(f"Failed to process entry {entry.entry_id} with {type(stage_worker).__name__}")

            return success

        except Exception as e:
            logger.error(f"Error processing entry {entry.entry_id} with specialized worker: {e}")
            return False

    @contextmanager
    def _get_stage_session(self):
        """Get isolated database session for stage thread."""
        session = self.db_session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def stop(self, timeout: float = 30.0):
        """
        Gracefully stop all stage threads.

        Args:
            timeout: Maximum time to wait for threads to stop
        """
        logger.info(f"Stopping stage threads worker {self.worker_id}...")

        # Signal all threads to stop
        self.shutdown_event.set()

        # Wait for all threads to finish
        for stage, thread in self.stage_threads.items():
            try:
                thread.join(timeout=timeout / len(self.stage_threads))
                if thread.is_alive():
                    logger.warning(f"Stage thread {stage.value} did not stop within timeout")
                else:
                    logger.info(f"Stage thread {stage.value} stopped successfully")
            except Exception as e:
                logger.error(f"Error joining stage thread {stage.value}: {e}")

        # Wait for heartbeat thread to finish
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            logger.info("Waiting for heartbeat thread to finish...")
            self.heartbeat_thread.join(timeout=5.0)
            if self.heartbeat_thread.is_alive():
                logger.warning("Heartbeat thread did not finish within timeout")
            else:
                logger.info("Heartbeat thread finished")

        # Stop recovery system
        if self._recovery_system:
            try:
                logger.info("Stopping worker recovery system...")
                self._recovery_system.stop(timeout=10)
                logger.info("Worker recovery system stopped")
            except Exception as e:
                logger.error(f"Error stopping recovery system: {e}")

        # Unregister worker from health monitoring
        self._unregister_worker()

        logger.info(f"Stage threads worker {self.worker_id} stopped")

    def _register_worker(self) -> None:
        """Register worker with health monitoring system."""
        try:
            from backend.app.distributed.worker_health_manager import WorkerHealthManager
            from backend.app.distributed.worker_config import WorkerConfig
            from backend.app.distributed.progress_reporter import ProgressReporter

            db_session = self.db_session_factory()
            self._health_manager = WorkerHealthManager(db_session)

            # Initialize progress reporter
            self._progress_reporter = ProgressReporter(self.worker_id, self.db_session_factory)

            # Create a config for registration
            config = WorkerConfig(
                worker_id=self.worker_id,
                stages=self.stages,
                batch_size=10,  # Default batch size
                heartbeat_interval_seconds=30
            )

            # Register worker in database
            self._health_manager.register_worker(
                worker_id=self.worker_id,
                worker_type="OneThreadPerStageWorker",
                config=config
            )

            logger.info(f"Registered parallel worker {self.worker_id} with health monitoring")

        except Exception as e:
            logger.error(f"Failed to register worker with health monitoring: {e}")
            # Don't fail startup if health registration fails
            self._health_manager = None
            self._progress_reporter = None
        finally:
            if 'db_session' in locals():
                db_session.close()

    def _unregister_worker(self) -> None:
        """Unregister worker from health monitoring system."""
        try:
            if self._health_manager:
                self._health_manager.unregister_worker(self.worker_id)
                logger.info(f"Unregistered parallel worker {self.worker_id} from health monitoring")
        except Exception as e:
            logger.error(f"Failed to unregister worker from health monitoring: {e}")

    def _start_heartbeat_thread(self) -> None:
        """Start the heartbeat thread."""
        if self._health_manager:
            self.heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop,
                name="HeartbeatThread",
                daemon=True
            )
            self.heartbeat_thread.start()
            logger.info("Started heartbeat thread")

    def _heartbeat_loop(self) -> None:
        """Heartbeat loop to indicate worker is alive and report progress."""
        logger.debug(f"Starting heartbeat loop for worker {self.worker_id}")

        while not self.shutdown_event.is_set():
            try:
                self._send_heartbeat()

                # Report progress for all stages periodically
                if self._progress_reporter:
                    self._progress_reporter.calculate_and_report_progress(self.stages)

                # Wait for next heartbeat interval (30 seconds)
                self.shutdown_event.wait(30.0)
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}", exc_info=True)
                self.shutdown_event.wait(5.0)  # Wait before retry

    def _send_heartbeat(self) -> None:
        """Send heartbeat to indicate worker is alive."""
        try:
            if self._health_manager:
                # Calculate total stats across all stages
                total_batches = sum(stats['batches_processed'] for stats in self.stage_stats.values())
                total_entries = sum(stats['entries_processed'] for stats in self.stage_stats.values())
                total_errors = sum(stats['errors'] for stats in self.stage_stats.values())

                stats = {
                    'batches_processed': total_batches,
                    'entries_processed': total_entries,
                    'entries_failed': total_errors,
                    'total_processing_time': 0.0  # Could be calculated if needed
                }

                self._health_manager.send_heartbeat(
                    worker_id=self.worker_id,
                    stats=stats,
                    current_batch_size=0,  # Not applicable for parallel worker
                    processing_since=None,
                    error_message=None
                )
        except Exception as e:
            logger.error(f"Failed to send heartbeat: {e}")

    def _update_worker_progress(self, stage: ProcessingStage, processed_count: int, total_count: int) -> None:
        """Update worker progress in health monitoring system."""
        try:
            if self._progress_reporter:
                # Update progress for this specific stage
                progress_data = {
                    'processed': processed_count,
                    'total': total_count,
                    'pending': max(0, total_count - processed_count),
                    'in_progress': 0,  # Batch is completed when this is called
                    'completed': processed_count,
                    'failed': 0  # Could be tracked if needed
                }
                self._progress_reporter.update_stage_progress(stage, progress_data)
        except Exception as e:
            logger.debug(f"Failed to update worker progress: {e}")

    def _process_singleton_stage(self, work_queue, stage) -> bool:
        """
        Process a singleton stage that doesn't require entry retrieval.

        Args:
            work_queue: WorkQueueManager instance for stage claiming
            stage: Singleton processing stage to work on

        Returns:
            True if work was processed, False if no work available or stage already claimed
        """
        from backend.app.distributed.processing_stage import stage_requires_singleton

        if not stage_requires_singleton(stage):
            logger.error(f"Stage {stage.value} is not a singleton stage")
            return False

        try:
            # Check if stage is in cooldown period
            if work_queue.is_stage_in_cooldown(stage):
                logger.debug(f"Singleton stage {stage.value} is in cooldown period")
                return False

            # Check if stage is already claimed by another worker
            if work_queue.is_singleton_stage_claimed(stage):
                logger.debug(f"Singleton stage {stage.value} is already claimed by another worker")
                return False

            # Attempt to claim the singleton stage using base worker ID
            if not work_queue.claim_singleton_stage(stage, self.worker_id):
                logger.debug(f"Failed to claim singleton stage {stage.value}")
                return False

            logger.info(f"Successfully claimed singleton stage {stage.value}")

            try:
                # Update progress to show processing started (1 in_progress)
                self._update_singleton_progress(stage, in_progress=1, completed=0)

                # Get the specialized worker for this stage
                if stage in self.stage_workers:
                    specialized_worker = self.stage_workers[stage]

                    # Process the singleton stage (no entries needed)
                    results = specialized_worker.process_batch([], stage)

                    # Update progress to show processing completed
                    success_count = len([r for r in results.values() if r]) if isinstance(results, dict) else 1
                    self._update_singleton_progress(stage, in_progress=0, completed=success_count)

                    # Record successful execution for cooldown tracking
                    # Use the base worker ID (without stage suffix) for database lookup
                    work_queue.record_stage_execution(stage, self.worker_id)

                    logger.info(f"Singleton stage {stage.value} processed successfully: {results}")
                    return True
                else:
                    logger.warning(f"No specialized worker found for singleton stage {stage.value}")
                    # Update progress to show processing failed
                    self._update_singleton_progress(stage, in_progress=0, completed=0)
                    return False

            finally:
                # Always release the singleton stage claim
                work_queue.release_singleton_stage(stage, self.worker_id)
                logger.debug(f"Released singleton stage {stage.value}")

        except Exception as e:
            logger.error(f"Error processing singleton stage {stage.value}: {e}", exc_info=True)
            try:
                # Ensure stage is released on error
                work_queue.release_singleton_stage(stage, self.worker_id)
            except Exception as release_error:
                logger.error(f"Error releasing singleton stage {stage.value}: {release_error}")
            return False

    def _update_singleton_progress(self, stage: ProcessingStage, in_progress: int, completed: int) -> None:
        """Update progress for singleton stage processing."""
        try:
            if self._progress_reporter:
                # For singleton stages, we need to calculate the total available work
                # This could be RSS sources for download_feeds
                total = in_progress + completed
                if total == 0:
                    total = 1  # At least 1 for singleton stages

                pending = max(0, total - in_progress - completed)

                progress_data = {
                    'pending': pending,
                    'in_progress': in_progress,
                    'completed': completed,
                    'failed': 0,
                    'total': total
                }

                self._progress_reporter.update_stage_progress(stage, progress_data)
                logger.debug(f"Updated singleton progress for {stage.value}: {progress_data}")
        except Exception as e:
            logger.debug(f"Failed to update singleton progress: {e}")

    def _start_recovery_system(self):
        """Start the worker recovery system for timeout management."""
        try:
            from backend.app.distributed.worker_recovery_system import create_default_recovery_system

            # Create recovery system with 3 min -> stopped, 5 min -> error
            self._recovery_system = create_default_recovery_system(
                db_session_factory=self.db_session_factory,
                check_interval_seconds=60,  # Check every minute
                stale_threshold_minutes=5,  # Mark as error after 5 minutes
                claim_timeout_minutes=30    # Release claims after 30 minutes
            )

            # Start the recovery system
            self._recovery_system.start()
            logger.info("Started worker recovery system for timeout management")

        except Exception as e:
            logger.error(f"Failed to start recovery system: {e}")
            # Continue without recovery system - not critical for basic operation

    def get_status(self) -> Dict:
        """Get comprehensive status of all stage threads."""
        return {
            'worker_id': self.worker_id,
            'worker_type': 'one_thread_per_stage',
            'total_stages': len(self.stages),
            'active_threads': sum(1 for t in self.stage_threads.values() if t.is_alive()),
            'shutdown_requested': self.shutdown_event.is_set(),
            'stage_stats': self.stage_stats,
            'stage_threads': {
                stage.value: {
                    'alive': thread.is_alive(),
                    'name': thread.name
                }
                for stage, thread in self.stage_threads.items()
            }
        }


def create_default_stage_worker(stages: Optional[List[ProcessingStage]] = None) -> OneThreadPerStageWorker:
    """
    Create a one-thread-per-stage worker with default configuration.

    Args:
        stages: List of stages to process. If None, uses all stages except DOWNLOAD_FEEDS
    """
    if stages is None:
        stages = [s for s in ProcessingStage if s != ProcessingStage.DOWNLOAD_FEEDS]

    return OneThreadPerStageWorker(stages)


def start_monolithic_style_threads(stages: Optional[List[ProcessingStage]] = None) -> OneThreadPerStageWorker:
    """
    Start threads in monolithic ETL style - one thread per stage.

    This function creates and starts threads exactly like the monolithic ETL:
    threading.Thread(target=aggregator.download_full_texts, name="DownloadFullTextsThread").start()
    threading.Thread(target=aggregator.llm_sentiment_ad, name="LLMSentimentAdThread").start()
    etc.

    Args:
        stages: List of stages to process. If None, uses all stages except DOWNLOAD_FEEDS

    Returns:
        OneThreadPerStageWorker instance with all threads started
    """
    worker = create_default_stage_worker(stages)
    worker.start()

    logger.info("Started monolithic-style stage threads:")
    for stage in worker.stages:
        logger.info(f"  - {stage.value}Thread (matches monolithic ETL)")

    return worker
