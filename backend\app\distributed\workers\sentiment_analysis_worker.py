"""
Sentiment analysis worker for distributed ETL processing.

This module provides the SentimentAnalysisWorker class that handles sentiment
analysis of news articles using multiple models in batch processing mode.
"""

import logging
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from backend.app.distributed.distributed_worker import DistributedWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class SentimentAnalysisWorker(DistributedWorker):
    """
    Worker specialized in sentiment analysis of news articles.
    
    Adapts the existing sentiment analysis logic for batch processing with
    batch-optimized model loading, proper resource management, and error handling.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the sentiment analysis worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.SENTIMENT_ANALYSIS not in config.stages:
            raise ValueError("SentimentAnalysisWorker requires SENTIMENT_ANALYSIS stage")
        
        # Initialize models (lazy loading)
        self._german_sentiment_model = None
        self._vader_analyzer = None
        self._textblob_initialized = False
        
        # Configuration for sentiment analysis

        self.preload_models = config.get_stage_config(
            ProcessingStage.SENTIMENT_ANALYSIS, 'preload_models', False
        )
        
        logger.info(
            f"Initialized SentimentAnalysisWorker {self.worker_id} "
            f"(preload: {self.preload_models})"
        )
        
        # Preload models if configured
        if self.preload_models:
            self._initialize_models()
    
    def _initialize_models(self) -> None:
        """Initialize sentiment analysis models."""
        try:
            logger.info("Initializing sentiment analysis models...")
            
            # Initialize German sentiment model
            from germansentiment import SentimentModel
            self._german_sentiment_model = SentimentModel()
            logger.info("German sentiment model loaded")
            
            # Initialize VADER analyzer
            from nltk.sentiment.vader import SentimentIntensityAnalyzer
            self._vader_analyzer = SentimentIntensityAnalyzer()
            logger.info("VADER analyzer loaded")
            
            # Initialize TextBlobDE (import check)
            from textblob_de import TextBlobDE
            self._textblob_initialized = True
            logger.info("TextBlobDE initialized")
            
            logger.info("All sentiment analysis models initialized successfully")
            
        except ImportError as e:
            logger.error(f"Failed to import required sentiment analysis libraries: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize sentiment analysis models: {e}")
            raise
    
    def _get_german_sentiment_model(self):
        """Get German sentiment model, initializing if needed."""
        if self._german_sentiment_model is None:
            from germansentiment import SentimentModel
            self._german_sentiment_model = SentimentModel()
            logger.debug("Lazy-loaded German sentiment model")
        return self._german_sentiment_model
    
    def _get_vader_analyzer(self):
        """Get VADER analyzer, initializing if needed."""
        if self._vader_analyzer is None:
            from nltk.sentiment.vader import SentimentIntensityAnalyzer
            self._vader_analyzer = SentimentIntensityAnalyzer()
            logger.debug("Lazy-loaded VADER analyzer")
        return self._vader_analyzer
    
    def _get_textblob_de(self):
        """Get TextBlobDE class, importing if needed."""
        if not self._textblob_initialized:
            from textblob_de import TextBlobDE
            self._textblob_initialized = True
            logger.debug("Lazy-loaded TextBlobDE")
        from textblob_de import TextBlobDE
        return TextBlobDE
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for sentiment analysis.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be SENTIMENT_ANALYSIS)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.SENTIMENT_ANALYSIS:
            logger.error(f"SentimentAnalysisWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for sentiment analysis")
        
        # Filter entries that have required text fields
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.full_text or not entry.english_text:
                logger.warning(
                    f"Entry {entry.entry_id} missing required text fields "
                    f"(full_text: {bool(entry.full_text)}, english_text: {bool(entry.english_text)})"
                )
                results[entry.entry_id] = False
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.warning("No valid entries to process in batch")
            return results
        
        # Process valid entries using batch processing for optimal performance
        batch_results = self._process_batch(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed sentiment analysis batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_batch(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries using batch processing for optimal performance.

        Args:
            entries: List of valid entries to process

        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        try:
            # Get models once for the entire batch
            german_model = self._get_german_sentiment_model()
            vader_analyzer = self._get_vader_analyzer()
            TextBlobDE = self._get_textblob_de()
            
            # Prepare batch data for German sentiment model
            german_texts = [entry.full_text for entry in entries]
            
            # Batch process German sentiment analysis
            logger.debug(f"Processing {len(german_texts)} texts with German sentiment model")
            predictions, probabilities = german_model.predict_sentiment(
                german_texts, output_probabilities=True
            )
            
            # Process each entry with all models
            for i, entry in enumerate(entries):
                try:
                    # VADER sentiment analysis (English text)
                    vader_scores = vader_analyzer.polarity_scores(entry.english_text)
                    
                    # German sentiment results (from batch processing)
                    prob_dict = dict(probabilities[i])
                    
                    # TextBlobDE sentiment analysis
                    tbd = TextBlobDE(entry.full_text)
                    tbd_polarity = tbd.sentiment.polarity
                    tbd_subjectivity = tbd.sentiment.subjectivity
                    
                    # Update database
                    success = self._update_entry_sentiment(
                        entry, vader_scores, prob_dict, tbd_polarity, tbd_subjectivity
                    )
                    results[entry.entry_id] = success
                    
                    if success:
                        logger.debug(f"Successfully analyzed sentiment for entry {entry.entry_id}")
                    else:
                        logger.warning(f"Failed to update database for entry {entry.entry_id}")
                
                except Exception as e:
                    logger.error(f"Error processing entry {entry.entry_id}: {e}")
                    results[entry.entry_id] = False
        
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            # Mark all entries as failed
            for entry in entries:
                results[entry.entry_id] = False
        
        return results
    

    
    def _update_entry_sentiment(self, entry: Entry, vader_scores: Dict[str, float],
                              german_probs: Dict[str, float], tbd_polarity: float,
                              tbd_subjectivity: float) -> bool:
        """
        Update the entry's sentiment analysis fields in the database.
        
        Args:
            entry: Entry object to update
            vader_scores: VADER sentiment scores
            german_probs: German sentiment probabilities
            tbd_polarity: TextBlobDE polarity score
            tbd_subjectivity: TextBlobDE subjectivity score
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Update sentiment analysis fields
                update_data = {
                    "vader_pos": vader_scores['pos'],
                    "vader_neu": vader_scores['neu'],
                    "vader_neg": vader_scores['neg'],
                    "vader_compound": vader_scores['compound'],
                    "german_sentiment_positive": german_probs['positive'],
                    "german_sentiment_neutral": german_probs['neutral'],
                    "german_sentiment_negative": german_probs['negative'],
                    "tbd_polarity": tbd_polarity,
                    "tbd_subjectivity": tbd_subjectivity
                }
                
                db_session.query(Entry).filter_by(entry_id=entry.entry_id).update(update_data)
                db_session.commit()
                
                logger.debug(f"Updated sentiment analysis for entry {entry.entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry.entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry.entry_id}: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to sentiment analysis worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add sentiment analysis specific metrics
        health.update({
            'worker_type': 'SentimentAnalysisWorker',
            'batch_optimize_models': self.batch_optimize_models,
            'preload_models': self.preload_models,
            'models_loaded': {
                'german_sentiment': self._german_sentiment_model is not None,
                'vader_analyzer': self._vader_analyzer is not None,
                'textblob_de': self._textblob_initialized
            },
            'supported_stages': [ProcessingStage.SENTIMENT_ANALYSIS.value]
        })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up model resources when worker shuts down."""
        logger.info("Cleaning up sentiment analysis model resources")
        
        # Clear model references to free memory
        self._german_sentiment_model = None
        self._vader_analyzer = None
        self._textblob_initialized = False
        
        logger.info("Sentiment analysis resources cleaned up")