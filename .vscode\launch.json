{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "etl_watchdog",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/etl/watchdog.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "development",
            },
            "justMyCode": false
        },
        {
            "name": "news_aggregator",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/etl/news_aggregator.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "development",
            },
            "justMyCode": false
        },
        {
            "name": "Python Debugger: FastAPI",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "backend.app.main:app",
                "--reload",
                "--host",
                "0.0.0.0",
                "--workers=1",
                "--reload-dir=backend/app",
                "--log-level=trace"
            ],
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "jinja": true,
            "justMyCode": false
        },
        {
            "name": "Flutter: Debug (Local Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=ENVIRONMENT=development"
            ]
        },
        {
            "name": "Flutter: Release (Local Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define=ENVIRONMENT=development"
            ]
        },
        {
            "name": "Flutter: Debug (Production Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=ENVIRONMENT=production"
            ]
        },
        {
            "name": "Flutter: Release (Production Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define=ENVIRONMENT=production",
                // "--no-enable-impeller"
            ]
        },
        {
            "name": "positive_news_app (legacy)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "ETL: Feed Download",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "DEBUG",
                "start",
                "--worker-id",
                "feed-worker-001",
                "--stages",
                "download_feeds",
                "--batch-size",
                "50"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Full Text Download",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "DEBUG",
                "start",
                "--worker-id",
                "fulltext-worker-001",
                "--stages",
                "download_full_text",
                "--batch-size",
                "25"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: LLM Analysis",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "DEBUG",
                "start",
                "--worker-id",
                "llm-worker-001",
                "--stages",
                "llm_analysis",
                "--batch-size",
                "10"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Embeddings",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "DEBUG",
                "start",
                "--worker-id",
                "embedding-worker-001",
                "--stages",
                "compute_embeddings",
                "--batch-size",
                "15"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Parallel (download_feeds)",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli.parallel_worker_cli",
            "args": [
                "--log-level",
                "DEBUG",
                "monolithic",
                "--stages",
                "download_feeds",
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Parallel (compute_embeddings)",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli.parallel_worker_cli",
            "args": [
                "--log-level",
                "DEBUG",
                "monolithic",
                "--stages",
                "compute_embeddings",
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Parallel (duplicate_check)",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli.parallel_worker_cli",
            "args": [
                "--log-level",
                "DEBUG",
                "monolithic",
                "--stages",
                "duplicate_check",
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Parallel (llm_analysis)",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli.parallel_worker_cli",
            "args": [
                "--log-level",
                "DEBUG",
                "monolithic",
                "--stages",
                "llm_analysis",
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Multi-Stage (all)",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli.parallel_worker_cli",
            "args": [
                "--log-level",
                "DEBUG",
                "monolithic",
                "--stages",
                "download_feeds",
                "download_full_text",
                "compute_embeddings",
                "llm_analysis",
                "iptc_classification",
                "duplicate_check",
                "generate_descriptions",
                "generate_image_prompts",
                "generate_preview_images"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: List Workers",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "INFO",
                "list",
                "--format",
                "table"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Queue Status",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "INFO",
                "queue-status",
                "--format",
                "table"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Cleanup Stale Work",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "INFO",
                "cleanup",
                "--timeout",
                "30"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Manager INFO",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "INFO",
                "manager",
                "--port",
                "8080",
                "--workers",
                "1"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "env":{
                "WORKER_STAGES_EXCLUDE": "translate_text,generate_rss_feed,sentiment_analysis"
            },
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Manager DEBUG",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.cli",
            "args": [
                "--log-level",
                "DEBUG",
                "manager",
                "--port",
                "8080",
                "--workers",
                "1"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        },
        {
            "name": "ETL: Dashboard DEBUG",
            "type": "debugpy",
            "request": "launch",
            "module": "backend.app.distributed.dashboard.app",
            "args": [
                "--log-level",
                "DEBUG"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}",
            "justMyCode": false
        }
    ]
}