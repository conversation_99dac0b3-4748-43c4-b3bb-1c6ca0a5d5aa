{% extends "base.html" %}

{% block title %}Workers - Distributed ETL{% endblock %}

{% block content %}

<style>
/* Custom blur tooltip */
.custom-blur-tooltip {
    position: fixed;
    z-index: 1070;
    pointer-events: none;
    opacity: 0;
    transform: translateY(0);
    transition: all 0.0s cubic-bezier(0.16, 1, 0.3, 1);
    max-width: 500px;
}

.custom-blur-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.custom-blur-tooltip-content {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(8px) saturate(120%);
    -webkit-backdrop-filter: blur(20px) saturate(120%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.3),
        0 10px 10px -5px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    overflow: hidden; /* ADD THIS */
}

/* ADD THIS: Make table fit within tooltip */
.custom-blur-tooltip-content table {
    width: 100%;
    max-width: 100%;
    table-layout: auto;
    margin-bottom: 0;
}

/* ADD THIS: Prevent table cells from overflowing */
.custom-blur-tooltip-content td,
.custom-blur-tooltip-content th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* Additional blur layer for stronger frosted effect */
.custom-blur-tooltip-content::after {
    content: '';
    position: absolute;
    inset: -5px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    z-index: -1;
    pointer-events: none;
}

/* Arrow */
.custom-blur-tooltip-content::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 8px 8px 0 8px;
    border-color: rgba(255, 255, 255, 0.25) transparent transparent transparent;
}

/* Hover state for progress summary */
.progress-summary {
    cursor: help;
    transition: all 0.15s ease;
}

.progress-summary:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 4px;
}
</style>

<!-- Custom Blur Tooltip Container -->
<div id="customBlurTooltip" class="custom-blur-tooltip">
    <div class="custom-blur-tooltip-content"></div>
</div>

<div class="row">
    <!-- Worker Status Overview -->
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Worker Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h3 class="text-success" id="running-count">--</h3>
                        <small class="text-muted">Running</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-secondary" id="stopped-count">--</h3>
                        <small class="text-muted">Stopped</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-danger" id="error-count">--</h3>
                        <small class="text-muted">Error</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Worker Actions -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Worker Management
                </h5>
                <div>
                    <button class="btn btn-sm btn-success" onclick="startAllWorkers()">
                        <i class="fas fa-play"></i> Start All
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="stopAllWorkers()">
                        <i class="fas fa-stop"></i> Stop All
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearErrorWorkers()">
                        <i class="fas fa-trash"></i> Clear Error Workers
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshWorkers()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Worker management functionality will be implemented in the next phase.
                    Currently showing read-only worker status.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Worker List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Active Workers
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="cursor: pointer;" onclick="sortWorkers('worker_id')">Worker ID <i class="fas fa-sort"></i></th>
                                <th style="cursor: pointer;" onclick="sortWorkers('status')">Status <i class="fas fa-sort"></i></th>
                                <th>Stages</th>
                                <th>Progress</th>
                                <th style="cursor: pointer;" onclick="sortWorkers('last_heartbeat')">Last Heartbeat <i class="fas fa-sort-down"></i></th>
                                <th style="cursor: pointer;" onclick="sortWorkers('started_at')">Started At <i class="fas fa-sort"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="workers-table">
                            <tr>
                                <td colspan="6" class="text-center text-muted">Loading workers...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Worker Details Modal -->
<div class="modal fade" id="workerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Worker Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="worker-details">
                <!-- Worker details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
/* --- ENABLE TABLES IN BOOTSTRAP SANITIZER (run immediately) --- */
(function configureBootstrapTooltipSanitizer() {
    if (window.bootstrap && bootstrap.Tooltip && bootstrap.Tooltip.Default) {
        const allowList = bootstrap.Tooltip.Default.allowList;

        // ensure table elements exist
        allowList.table  = allowList.table  || [];
        allowList.thead  = allowList.thead  || [];
        allowList.tbody  = allowList.tbody  || [];
        allowList.tr     = allowList.tr     || [];
        allowList.td     = allowList.td     || [];
        allowList.th     = allowList.th     || [];

        // allow class on structural elements so bootstrap table classes survive
        ['table','thead','tbody','tr'].forEach(tag => {
            if (!allowList[tag].includes('class')) allowList[tag].push('class');
        });

        // allow common cell attributes
        ['td','th'].forEach(tag => {
            ['rowspan','colspan','class'].forEach(attr => {
                if (!allowList[tag].includes(attr)) allowList[tag].push(attr);
            });
        });
    }
})();

let workersData = [];
let sortColumn = 'last_heartbeat';
let sortDirection = 'desc';

// Format timestamp to Berlin timezone
function formatBerlinTime(timestamp) {
    if (!timestamp) return 'N/A';
    try {
        // Remove microseconds for both Z and +00:00 endings
        let cleaned = timestamp.replace(/\.(\d{3,6})((Z)|([+-]\d{2}:?\d{2}))$/, '$2');
        cleaned = cleaned.replace(' ', 'T');
        if (!/[Z+-]/.test(cleaned)) cleaned += 'Z';
        const utcDate = new Date(cleaned);
        if (isNaN(utcDate.getTime())) return timestamp;

        // Calculate Berlin offset (CET/CEST)
        // Berlin is UTC+1 in winter, UTC+2 in summer
        // Get last Sunday in March and October
        function getLastSunday(year, month) {
            const d = new Date(Date.UTC(year, month + 1, 0));
            d.setUTCDate(d.getUTCDate() - d.getUTCDay());
            return d;
        }
        const year = utcDate.getUTCFullYear();
        const dstStart = getLastSunday(year, 2); // March
        dstStart.setUTCHours(1, 0, 0, 0); // 2:00 local time (1:00 UTC)
        const dstEnd = getLastSunday(year, 9); // October
        dstEnd.setUTCHours(1, 0, 0, 0); // 2:00 local time (1:00 UTC)

        let offset = 1; // default CET
        if (utcDate >= dstStart && utcDate < dstEnd) {
            offset = 2; // CEST
        }
        // Add offset in hours
        const berlinDate = new Date(utcDate.getTime() + offset * 60 * 60 * 1000);
        // Format in German locale
        return berlinDate.toLocaleString('de-DE', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (e) {
        return timestamp;
    }
}

// Format progress data for display
function formatProgressSummary(progressData) {
    if (!progressData) return { summary: 'No data', tooltip: '' };

    try {
        const progress = typeof progressData === 'string' ? JSON.parse(progressData) : progressData;
        const stages = Object.keys(progress);

        if (stages.length === 0) return { summary: 'No stages', tooltip: '' };

        let totalCompleted = 0;
        let totalTotal = 0;
        const tooltipRows = [];

        stages.forEach(stage => {
            const stageData = progress[stage];
            const completed = stageData.completed || 0;
            const total = stageData.total || 0;
            const pending = stageData.pending || 0;
            const inProgress = stageData.in_progress || 0;

            totalCompleted += completed;
            totalTotal += total;

            tooltipRows.push(`
                <tr>
                    <td><strong>${stage}</strong></td>
                    <td>${pending}</td>
                    <td>${inProgress}</td>
                    <td>${completed}</td>
                    <td>${total}</td>
                </tr>
            `);
        });

        const percentage = totalTotal > 0 ? Math.round((totalCompleted / totalTotal) * 100) : 0;
        const summary = `${totalCompleted}/${totalTotal} (${percentage}%)`;

        const tooltip = `
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>Stage</th>
                        <th>Pending</th>
                        <th>In Progress</th>
                        <th>Completed</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    ${tooltipRows.join('')}
                </tbody>
            </table>
        `;

        return { summary, tooltip };
    } catch (e) {
        return { summary: 'Invalid data', tooltip: 'Error parsing progress data' };
    }
}

async function loadWorkers() {
    try {
        const response = await fetch('/api/workers/status');
        const data = await response.json();
        
        workersData = data.workers;
        updateWorkerOverview(data);
        updateWorkersTable(data.workers);
        
    } catch (error) {
        console.error('Error loading workers:', error);
        showError('Failed to load worker data');
    }
}

function updateWorkerOverview(data) {
    document.getElementById('running-count').textContent = data.running;
    document.getElementById('stopped-count').textContent = data.stopped;
    document.getElementById('error-count').textContent = data.error;
}

function updateWorkersTable(workers) {
    const tbody = document.getElementById('workers-table');

    if (workers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No workers found</td></tr>';
        return;
    }

    // Sort workers
    const sortedWorkers = [...workers].sort((a, b) => { /* ... unchanged ... */ });

    tbody.innerHTML = sortedWorkers.map((worker, idx) => {
        const statusClass = getStatusClass(worker.status);
        const statusIcon = getStatusIcon(worker.status);
        const stages = Array.isArray(worker.stages) ? worker.stages.join(', ') : 'N/A';
        const lastHeartbeat = worker.last_heartbeat ? formatBerlinTime(worker.last_heartbeat) : 'Never';
        const startedAt = worker.started_at ? formatBerlinTime(worker.started_at) : 'N/A';

        const progressInfo = formatProgressSummary(worker.progress);

        // Do NOT escape double quotes here; we will insert into a single-quoted attribute.
        // Escape single quotes so the single-quoted attribute won't break.
        let safeTooltip;
        safeTooltip = progressInfo.tooltip.replace(/\s+/g, ' ').replace(/'/g, '&#39;');

        return `
            <tr>
                <td><code>${worker.worker_id}</code></td>
                <td><span class="badge ${statusClass}"><i class="${statusIcon}"></i> ${worker.status}</span></td>
                <td><small>${stages}</small></td>
                <td>
                    <span class="progress-summary tooltip-trigger"
                        data-tooltip-content='${safeTooltip}'>
                        ${progressInfo.summary}
                    </span>
                </td>
                <td>${lastHeartbeat}</td>
                <td>${startedAt}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="showWorkerDetails('${worker.worker_id}')">
                        <i class="fas fa-info"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="restartWorker('${worker.worker_id}')" disabled>
                        <i class="fas fa-redo"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');

}

function getStatusClass(status) {
    switch (status) {
        case 'running': return 'bg-success';
        case 'stopped': return 'bg-secondary';
        case 'error': return 'bg-danger';
        default: return 'bg-warning';
    }
}

function getStatusIcon(status) {
    switch (status) {
        case 'running': return 'fas fa-play';
        case 'stopped': return 'fas fa-stop';
        case 'error': return 'fas fa-exclamation-triangle';
        default: return 'fas fa-question';
    }
}

function showWorkerDetails(workerId) {
    const worker = workersData.find(w => w.worker_id === workerId);
    if (!worker) return;

    // Format JSON data for display
    function formatJsonData(data, label) {
        if (!data) return '';
        try {
            const parsed = typeof data === 'string' ? JSON.parse(data) : data;
            return `
                <div class="mt-3">
                    <h6>${label}</h6>
                    <pre class="bg-light p-2 rounded" style="max-height: 300px; overflow-y: auto; font-size: 0.8em;"><code>${JSON.stringify(parsed, null, 2)}</code></pre>
                </div>
            `;
        } catch (e) {
            return `
                <div class="mt-3">
                    <h6>${label}</h6>
                    <div class="text-muted">Invalid JSON data</div>
                </div>
            `;
        }
    }

    const details = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Worker ID:</strong></td><td><code>${worker.worker_id}</code></td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusClass(worker.status)}">${worker.status}</span></td></tr>
                    <tr><td><strong>Started At:</strong></td><td>${worker.started_at ? formatBerlinTime(worker.started_at) : 'N/A'}</td></tr>
                    <tr><td><strong>Last Heartbeat:</strong></td><td>${worker.last_heartbeat ? formatBerlinTime(worker.last_heartbeat) : 'Never'}</td></tr>
                    <tr><td><strong>Host:</strong></td><td>${worker.host || 'N/A'}</td></tr>
                    <tr><td><strong>PID:</strong></td><td>${worker.pid || 'N/A'}</td></tr>
                    <tr><td><strong>Version:</strong></td><td>${worker.version || 'N/A'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Processing Stages</h6>
                <div class="d-flex flex-wrap gap-1">
                    ${Array.isArray(worker.stages) ? worker.stages.map(stage =>
                        `<span class="badge bg-primary">${stage}</span>`
                    ).join('') : '<span class="text-muted">No stages configured</span>'}
                </div>

                <h6 class="mt-3">Additional Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Error Message:</strong></td><td>${worker.error_message || 'None'}</td></tr>
                    <tr><td><strong>Error Count:</strong></td><td>${worker.error_count || 0}</td></tr>
                    <tr><td><strong>Last Error:</strong></td><td>${worker.last_error ? formatBerlinTime(worker.last_error) : 'Never'}</td></tr>
                </table>
            </div>
        </div>

        ${formatJsonData(worker.progress, 'Progress Data')}
        ${formatJsonData(worker.config, 'Configuration')}
        ${formatJsonData(worker.metadata, 'Metadata')}
    `;

    document.getElementById('worker-details').innerHTML = details;
    new bootstrap.Modal(document.getElementById('workerModal')).show();
}

function sortWorkers(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = column === 'last_heartbeat' ? 'desc' : 'asc';
    }

    // Update table
    updateWorkersTable(workersData);

    // Update sort indicators
    const headers = document.querySelectorAll('#workers-table thead th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (header.textContent.toLowerCase().includes(column.replace('_', ' '))) {
                icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            } else {
                icon.className = 'fas fa-sort';
            }
        }
    });
}

async function clearErrorWorkers() {
    try {
        const response = await fetch('/api/workers/clear_errors', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.error) {
            console.error(`Error clearing workers: ${result.error}`);
            // Show error in status area instead of popup
            showStatus(`Error clearing workers: ${result.error}`, 'error');
        } else {
            // Log success but don't show popup
            console.log(`Cleared ${result.cleared_count} error workers`);
            if (result.cleared_count > 0) {
                showStatus(`Cleared ${result.cleared_count} error workers`, 'success');
            }
            loadWorkers(); // Refresh the table
        }
    } catch (error) {
        console.error(`Error: ${error.message}`);
        showStatus(`Error: ${error.message}`, 'error');
    }
}

function refreshWorkers() {
    loadWorkers();
}

function startAllWorkers() {
    alert('Start All Workers functionality will be implemented in the next phase.');
}

function stopAllWorkers() {
    alert('Stop All Workers functionality will be implemented in the next phase.');
}

function restartWorker(workerId) {
    alert(`Restart Worker ${workerId} functionality will be implemented in the next phase.`);
}

function showError(message) {
    // Simple error display - could be enhanced with toast notifications
    console.error(message);
}

function showStatus(message, type = 'info') {
    // Create or update status message area
    let statusArea = document.getElementById('status-area');
    if (!statusArea) {
        statusArea = document.createElement('div');
        statusArea.id = 'status-area';
        statusArea.className = 'alert alert-dismissible fade show';
        statusArea.style.position = 'fixed';
        statusArea.style.top = '20px';
        statusArea.style.right = '20px';
        statusArea.style.zIndex = '9999';
        statusArea.style.minWidth = '300px';
        document.body.appendChild(statusArea);
    }

    // Set alert type
    statusArea.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;

    statusArea.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Auto-hide after 3 seconds
    setTimeout(() => {
        if (statusArea && statusArea.parentNode) {
            statusArea.remove();
        }
    }, 3000);
}

function updateDashboard(data) {
    // Handle WebSocket updates
    if (data.type === 'worker_update') {
        loadWorkers();
    }
}

class CustomBlurTooltip {
    constructor() {
        this.tooltip = document.getElementById('customBlurTooltip');
        this.tooltipContent = this.tooltip.querySelector('.custom-blur-tooltip-content');
        this.currentTrigger = null;
        this.hideTimeout = null;
        
        this.initEventListeners();
    }

    initEventListeners() {
        document.addEventListener('mouseenter', (e) => {
            const target = e.target;
            if (target && target.classList && target.classList.contains('tooltip-trigger')) {
                this.show(target);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            const target = e.target;
            if (target && target.classList && target.classList.contains('tooltip-trigger')) {
                this.hide();
            }
        }, true);

        document.addEventListener('scroll', () => this.hide(), true);
        window.addEventListener('resize', () => this.hide());
    }

    show(trigger) {
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }

        this.currentTrigger = trigger;
        trigger.classList.add('active');

        const content = trigger.getAttribute('data-tooltip-content') || 'No content';
        this.tooltipContent.innerHTML = content;

        // Position immediately without setTimeout
        requestAnimationFrame(() => {
            this.position(trigger);
            this.tooltip.classList.add('show');
        });
    }

    hide() {
        this.tooltip.classList.remove('show');
        if (this.currentTrigger) {
            this.currentTrigger.classList.remove('active');
            this.currentTrigger = null;
        }
    }

    position(trigger) {
        const triggerRect = trigger.getBoundingClientRect();
        
        // Make tooltip visible but off-screen for measurement
        this.tooltip.style.opacity = '0';
        this.tooltip.style.display = 'block';
        this.tooltip.classList.add('show');
        
        // Force layout calculation
        const tooltipRect = this.tooltip.getBoundingClientRect();
        
        // Reset opacity (will fade in via CSS transition)
        this.tooltip.style.opacity = '';

        const gap = 12;
        let left = triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2);
        let top = triggerRect.top - tooltipRect.height - gap;

        // Keep tooltip within viewport
        const padding = 20;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Adjust horizontal position if needed
        if (left < padding) {
            left = padding;
        } else if (left + tooltipRect.width > viewportWidth - padding) {
            left = viewportWidth - tooltipRect.width - padding;
        }
        
        // Adjust vertical position if needed
        if (top < padding) {
            // Show below trigger if not enough space above
            top = triggerRect.bottom + gap;
        }

        this.tooltip.style.left = left + 'px';
        this.tooltip.style.top = top + 'px';
    }
}

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    // Initialize custom blur tooltip
    new CustomBlurTooltip();
    
    loadWorkers();

    // DEBUG: Initialize static test tooltip (uses default config)
    setTimeout(() => {
        const testBtn = document.getElementById('test-tooltip-btn');
        if (testBtn) {
            try {
                // static test does not contain a table, default sanitizer remains enabled
                new bootstrap.Tooltip(testBtn);
            } catch (err) {
                console.error('Static test tooltip error:', err);
            }
        }
    }, 0);

    // Refresh every 30 seconds
    setInterval(loadWorkers, 30000);
});
</script>
{% endblock %}