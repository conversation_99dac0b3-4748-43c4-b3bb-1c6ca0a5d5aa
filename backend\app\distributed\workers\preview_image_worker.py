"""
Preview Image worker for distributed ETL processing.

This module provides the PreviewImageWorker class that handles generating
preview images for news entries using various image generation APIs in batch processing mode.
"""

import logging
import base64
import requests
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from backend.app.distributed.distributed_worker import Distri<PERSON>Worker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class PreviewImageWorker(DistributedWorker):
    """
    Worker specialized in generating preview images for news entries using image generation APIs.
    
    Adapts the existing generate_preview_images logic for batch processing with
    proper rate limiting for image generation APIs and batch optimization.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the preview image worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.GENERATE_PREVIEW_IMAGES not in config.stages:
            raise ValueError("PreviewImageWorker requires GENERATE_PREVIEW_IMAGES stage")
        
        # Configuration for preview image generation
        self.sentiment_threshold = config.get_stage_config(
            ProcessingStage.GENERATE_PREVIEW_IMAGES, 'sentiment_threshold', 0.5
        )
        self.ad_threshold = config.get_stage_config(
            ProcessingStage.GENERATE_PREVIEW_IMAGES, 'ad_threshold', 0.5
        )

        
        # Image generation providers configuration
        self._providers = config.get_stage_config(
            ProcessingStage.GENERATE_PREVIEW_IMAGES, 'image_providers', 
            ['stable_diffusion', 'together', 'ionos']
        )
        
        # Provider-specific configurations
        self._provider_configs = config.get_stage_config(
            ProcessingStage.GENERATE_PREVIEW_IMAGES, 'provider_configs', {}
        )
        
        logger.info(
            f"Initialized PreviewImageWorker {self.worker_id} "
            f"(sentiment_threshold: {self.sentiment_threshold}, "
            f"ad_threshold: {self.ad_threshold}, "
            f"providers: {self._providers})"
        )
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for preview image generation.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be GENERATE_PREVIEW_IMAGES)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.GENERATE_PREVIEW_IMAGES:
            logger.error(f"PreviewImageWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for preview image generation")
        
        # Filter entries that have required fields and meet criteria
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.full_text:
                logger.warning(
                    f"Entry {entry.entry_id} missing required full_text field"
                )
                results[entry.entry_id] = False
            elif not entry.image_prompt:
                logger.warning(
                    f"Entry {entry.entry_id} missing required image_prompt field"
                )
                results[entry.entry_id] = False
            elif entry.preview_img:
                logger.debug(
                    f"Entry {entry.entry_id} already has preview image, skipping"
                )
                results[entry.entry_id] = True  # Already has image, consider success
            elif hasattr(entry, 'llm_positive') and entry.llm_positive is not None and entry.llm_positive < self.sentiment_threshold:
                logger.debug(
                    f"Entry {entry.entry_id} sentiment score {entry.llm_positive} below threshold {self.sentiment_threshold}, skipping"
                )
                results[entry.entry_id] = True  # Doesn't meet criteria, but not an error
            elif hasattr(entry, 'llm_is_ad') and entry.llm_is_ad is not None and entry.llm_is_ad >= self.ad_threshold:
                logger.debug(
                    f"Entry {entry.entry_id} ad score {entry.llm_is_ad} above threshold {self.ad_threshold}, skipping"
                )
                results[entry.entry_id] = True  # Doesn't meet criteria, but not an error
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.info("No valid entries to process in batch")
            return results
        
        # Process valid entries individually
        batch_results = self._process_entries(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed preview image generation batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_entries(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries for preview image generation.

        Args:
            entries: List of valid entries to process

        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        for entry in entries:
            try:
                # Check rate limits before making call
                if not self._can_make_image_call():
                    logger.warning(f"Rate limit reached, deferring entry {entry.entry_id}")
                    results[entry.entry_id] = False
                    continue
                
                # Generate image using provider fallback
                logger.debug(f"Generating preview image for entry {entry.entry_id}")
                image_data, model, provider = self._generate_image_with_fallback(entry.image_prompt)
                
                if not image_data:
                    logger.warning(f"No image data generated for entry {entry.entry_id}")
                    results[entry.entry_id] = False
                    continue
                
                # Update database
                success = self._update_entry_preview_image(entry.entry_id, image_data, model, provider)
                results[entry.entry_id] = success
                
                if success:
                    logger.debug(
                        f"Generated preview image for entry {entry.entry_id} using {provider} ({model})"
                    )
                else:
                    logger.warning(f"Failed to update database for entry {entry.entry_id}")
            
            except Exception as e:
                logger.error(f"Unexpected error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _can_make_image_call(self) -> bool:
        """
        Check if we can make an image generation call based on rate limits.
        
        Returns:
            True if calls can be made, False if rate limited
        """
        # Simple check - in a real implementation, this would check
        # rate limits for all providers and estimate capacity
        return True
    
    def _generate_image_with_fallback(self, prompt: str) -> Tuple[Optional[bytes], Optional[str], Optional[str]]:
        """
        Generate image using provider fallback logic.
        
        Args:
            prompt: Image generation prompt
            
        Returns:
            Tuple of (image_data, model_name, provider_name) or (None, None, None) if all fail
        """
        for provider in self._providers:
            try:
                image_data, model = self._generate_image_provider(provider, prompt)
                return image_data, model, provider
            except Exception as e:
                logger.warning(f"Image generation with {provider} failed: {e}")
                continue
        
        logger.error("All image generation providers failed")
        return None, None, None
    
    def _generate_image_provider(self, provider: str, prompt: str) -> Tuple[bytes, str]:
        """
        Generate image using specified provider.
        
        Args:
            provider: The image provider ('stable_diffusion', 'together', 'ionos', 'google')
            prompt: The image generation prompt
            
        Returns:
            Tuple of (image_data, model_name)
            
        Raises:
            Exception: If image generation fails
        """
        if provider == 'stable_diffusion':
            return self._generate_stable_diffusion(prompt)
        elif provider == 'together':
            return self._generate_together(prompt)
        elif provider == 'ionos':
            return self._generate_ionos(prompt)
        elif provider == 'google':
            return self._generate_google(prompt)
        else:
            raise ValueError(f"Unsupported image provider: {provider}")
    
    def _generate_stable_diffusion(self, prompt: str) -> Tuple[bytes, str]:
        """Generate image using local Stable Diffusion API."""
        base_url = self._provider_configs.get('stable_diffusion', {}).get('base_url', 'http://127.0.0.1:7860')
        
        negative_prompt = (
            "face, logo, brands, text, watermark, signature, blur, out of focus, "
            "deformed, bad anatomy, disfigured, poorly drawn face, mutated, extra limb, "
            "ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, "
            "malformed hands, long neck, long body, mutated hands and fingers"
        )
        
        payload = {
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "steps": 20,
            "width": 688,
            "height": 400,
            "cfg_scale": 7,
            "sampler_name": "DPM++ 2M Karras"
        }
        
        response = requests.post(
            url=f"{base_url}/sdapi/v1/txt2img",
            json=payload,
            timeout=120  # Image generation can take time
        )
        response.raise_for_status()
        
        response_data = response.json()
        image_data = base64.b64decode(response_data['images'][0])
        
        return image_data, "stable_diffusion_local"
    
    def _generate_together(self, prompt: str) -> Tuple[bytes, str]:
        """Generate image using Together AI API."""
        api_key = self._provider_configs.get('together', {}).get('api_key')
        if not api_key:
            raise ValueError("Together AI API key not provided")
        
        try:
            from openai import OpenAI
            
            client = OpenAI(
                api_key=api_key,
                base_url='https://api.together.xyz/v1'
            )
            
            response = client.images.generate(
                model='black-forest-labs/FLUX.1-schnell-Free',
                prompt=f"an illustration with {prompt}",
                size='1024x1024',
                response_format='b64_json'
            )
            
            image_data = base64.b64decode(response.data[0].b64_json)
            return image_data, "flux1_schnell_free_together"
            
        except ImportError:
            raise Exception("OpenAI library not available for Together AI integration")
    
    def _generate_ionos(self, prompt: str) -> Tuple[bytes, str]:
        """Generate image using IONOS API."""
        api_key = self._provider_configs.get('ionos', {}).get('api_key')
        if not api_key:
            raise ValueError("IONOS API key not provided")
        
        try:
            from openai import OpenAI
            
            client = OpenAI(
                api_key=api_key,
                base_url='https://openai.inference.de-txl.ionos.com/v1'
            )
            
            response = client.images.generate(
                model='black-forest-labs/FLUX.1-schnell',
                prompt=f"an illustration with {prompt}",
                size='1024x1024'
            )
            
            image_data = base64.b64decode(response.data[0].b64_json)
            return image_data, "flux1_schnell_ionos"
            
        except ImportError:
            raise Exception("OpenAI library not available for IONOS integration")
    
    def _generate_google(self, prompt: str) -> Tuple[bytes, str]:
        """Generate image using Google Gemini API."""
        api_key = self._provider_configs.get('google', {}).get('api_key')
        if not api_key:
            raise ValueError("Google API key not provided")
        
        model = "gemini-2.0-flash-preview-image-generation"
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"
        
        headers = {"Content-Type": "application/json"}
        data = {
            "contents": [{
                "parts": [
                    {"text": f"an illustration with {prompt}"}
                ]
            }],
            "generationConfig": {"responseModalities": ["TEXT", "IMAGE"]}
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=60)
        response.raise_for_status()
        
        response_json = response.json()
        image_data = base64.b64decode(
            response_json["candidates"][0]["content"]["parts"][1]['inlineData']['data']
        )
        
        return image_data, model
    
    def _update_entry_preview_image(self, entry_id: str, image_data: bytes, model: str, provider: str) -> bool:
        """
        Update the entry's preview image in the database.
        
        Args:
            entry_id: ID of the entry to update
            image_data: Generated image data as bytes
            model: Model name used for generation
            provider: Provider name used for generation
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Update preview image fields
                db_session.query(Entry).filter_by(entry_id=entry_id).update({
                    "preview_img": image_data,
                    "preview_model": model,
                    "preview_provider": provider
                })
                db_session.commit()
                
                logger.debug(f"Updated preview image for entry {entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry_id}: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to preview image worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add preview image worker specific metrics
        health.update({
            'worker_type': 'PreviewImageWorker',
            'sentiment_threshold': self.sentiment_threshold,
            'ad_threshold': self.ad_threshold,
            'image_providers': self._providers,
            'supported_stages': [ProcessingStage.GENERATE_PREVIEW_IMAGES.value]
        })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up resources when worker shuts down."""
        logger.info("Cleaning up preview image worker resources")
        
        # No specific resources to clean up for this worker
        logger.info("Preview image worker resources cleaned up")