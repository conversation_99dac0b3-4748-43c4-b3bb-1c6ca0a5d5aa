"""
Worker Recovery System for distributed ETL processing.

This module provides automatic worker failure detection and recovery functionality.
"""

import logging
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional, Callable
from sqlalchemy.orm import Session

from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.work_queue_manager import WorkQueueManager

logger = logging.getLogger(__name__)


class WorkerRecoverySystem:
    """
    Automatic worker failure detection and recovery system.
    
    Monitors worker health, detects failed workers, and automatically
    recovers by releasing claimed work and cleaning up stale resources.
    """
    
    def __init__(self, 
                 db_session_factory: Callable[[], Session],
                 check_interval_seconds: int = 60,
                 stale_threshold_minutes: int = 5,
                 claim_timeout_minutes: int = 30):
        """
        Initialize the recovery system.
        
        Args:
            db_session_factory: Factory function to create database sessions
            check_interval_seconds: How often to check for failed workers
            stale_threshold_minutes: Minutes after which a worker is considered failed
            claim_timeout_minutes: Minutes after which claims are released
        """
        self.db_session_factory = db_session_factory
        self.check_interval_seconds = check_interval_seconds
        self.stale_threshold_minutes = stale_threshold_minutes
        self.claim_timeout_minutes = claim_timeout_minutes
        
        # Recovery system state
        self._running = False
        self._recovery_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
        # Statistics
        self._stats = {
            'recovery_cycles': 0,
            'workers_recovered': 0,
            'entries_released': 0,
            'last_recovery_time': None,
            'last_error': None
        }
        
        # Callbacks for notifications
        self._failure_callbacks: List[Callable[[List[Dict[str, Any]]], None]] = []
        self._recovery_callbacks: List[Callable[[Dict[str, int]], None]] = []
    
    def start(self) -> None:
        """Start the recovery system."""
        if self._running:
            logger.warning("Recovery system is already running")
            return
        
        logger.info(
            f"Starting worker recovery system "
            f"(check_interval: {self.check_interval_seconds}s, "
            f"stale_threshold: {self.stale_threshold_minutes}m)"
        )
        
        self._running = True
        self._shutdown_event.clear()
        
        # Start recovery thread
        self._recovery_thread = threading.Thread(
            target=self._recovery_loop,
            name="worker-recovery",
            daemon=True
        )
        self._recovery_thread.start()
        
        logger.info("Worker recovery system started")
    
    def stop(self, timeout: int = 10) -> None:
        """Stop the recovery system."""
        if not self._running:
            return
        
        logger.info("Stopping worker recovery system...")
        
        self._running = False
        self._shutdown_event.set()
        
        # Wait for recovery thread to finish
        if self._recovery_thread and self._recovery_thread.is_alive():
            self._recovery_thread.join(timeout=timeout)
            
            if self._recovery_thread.is_alive():
                logger.warning("Recovery thread did not stop within timeout")
        
        logger.info("Worker recovery system stopped")
    
    def add_failure_callback(self, callback: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Add callback to be called when worker failures are detected."""
        self._failure_callbacks.append(callback)
    
    def add_recovery_callback(self, callback: Callable[[Dict[str, int]], None]) -> None:
        """Add callback to be called when recovery actions are taken."""
        self._recovery_callbacks.append(callback)
    
    def _recovery_loop(self) -> None:
        """Main recovery loop."""
        logger.info("Worker recovery loop started")
        
        while self._running and not self._shutdown_event.is_set():
            try:
                # Perform recovery check
                self._perform_recovery_check()
                
                # Wait for next check
                self._shutdown_event.wait(self.check_interval_seconds)
                
            except Exception as e:
                logger.error(f"Error in recovery loop: {e}", exc_info=True)
                self._stats['last_error'] = str(e)
                
                # Wait before retrying
                self._shutdown_event.wait(min(self.check_interval_seconds, 30))
        
        logger.info("Worker recovery loop stopped")
    
    def _perform_recovery_check(self) -> None:
        """Perform a single recovery check cycle."""
        db_session = None
        
        try:
            # Create database session
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            work_queue = WorkQueueManager(db_session)

            # Update worker timeouts (3 min -> stopped, 5 min -> error)
            timeout_stats = health_manager.update_worker_timeouts(
                stopped_threshold_minutes=3,
                error_threshold_minutes=5
            )

            # Detect stale workers
            stale_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_threshold_minutes
            )
            
            if stale_workers:
                logger.warning(f"Detected {len(stale_workers)} stale workers")
                
                # Notify failure callbacks
                for callback in self._failure_callbacks:
                    try:
                        callback(stale_workers)
                    except Exception as e:
                        logger.error(f"Error in failure callback: {e}")
                
                # Perform recovery
                recovery_stats = health_manager.recover_failed_workers(
                    stale_threshold_minutes=self.stale_threshold_minutes,
                    claim_timeout_minutes=self.claim_timeout_minutes
                )
                
                # Update statistics
                self._stats['workers_recovered'] += recovery_stats['workers_marked_failed']
                self._stats['entries_released'] += recovery_stats['entries_released']
                
                logger.info(
                    f"Recovery completed: {recovery_stats['workers_marked_failed']} workers failed, "
                    f"{recovery_stats['entries_released']} entries released"
                )
                
                # Notify recovery callbacks
                for callback in self._recovery_callbacks:
                    try:
                        callback(recovery_stats)
                    except Exception as e:
                        logger.error(f"Error in recovery callback: {e}")
            
            # Perform additional cleanup
            self._perform_additional_cleanup(work_queue)
            
            # Update statistics
            self._stats['recovery_cycles'] += 1
            self._stats['last_recovery_time'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            logger.error(f"Error performing recovery check: {e}", exc_info=True)
            self._stats['last_error'] = str(e)
            raise
        
        finally:
            if db_session:
                db_session.close()
    
    def _perform_additional_cleanup(self, work_queue: WorkQueueManager) -> None:
        """Perform additional cleanup operations."""
        try:
            # Create a separate session for cleanup to avoid transaction conflicts
            with self.db_session_factory() as cleanup_session:
                cleanup_work_queue = WorkQueueManager(cleanup_session)

                # Clean up very old stale claims (double the normal timeout)
                extended_timeout = self.claim_timeout_minutes * 2
                old_claims_cleaned = cleanup_work_queue.cleanup_stale_claims(extended_timeout)

                if old_claims_cleaned > 0:
                    logger.info(f"Cleaned up {old_claims_cleaned} very old stale claims")

        except Exception as e:
            logger.warning(f"Error in additional cleanup: {e}")
    
    def force_recovery_check(self) -> Dict[str, Any]:
        """
        Force an immediate recovery check.
        
        Returns:
            Dictionary with recovery results
        """
        if not self._running:
            raise RuntimeError("Recovery system is not running")
        
        logger.info("Forcing immediate recovery check")
        
        db_session = None
        try:
            # Create database session
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            
            # Detect and recover from failed workers
            stale_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_threshold_minutes
            )
            
            recovery_stats = health_manager.recover_failed_workers(
                stale_threshold_minutes=self.stale_threshold_minutes,
                claim_timeout_minutes=self.claim_timeout_minutes
            )
            
            # Add stale worker information
            recovery_stats['stale_workers'] = stale_workers
            recovery_stats['timestamp'] = datetime.now(timezone.utc).isoformat()
            
            logger.info(f"Forced recovery completed: {recovery_stats}")
            
            return recovery_stats
            
        except Exception as e:
            logger.error(f"Error in forced recovery check: {e}")
            raise
        
        finally:
            if db_session:
                db_session.close()
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery system statistics."""
        return {
            'running': self._running,
            'check_interval_seconds': self.check_interval_seconds,
            'stale_threshold_minutes': self.stale_threshold_minutes,
            'claim_timeout_minutes': self.claim_timeout_minutes,
            'stats': self._stats.copy()
        }
    
    def get_current_worker_status(self) -> Dict[str, Any]:
        """Get current status of all workers."""
        db_session = None
        try:
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            
            # Get all workers and their health
            all_workers = health_manager.get_all_workers_health()
            
            # Get stale workers
            stale_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_threshold_minutes
            )
            
            # Get worker metrics
            metrics = health_manager.get_worker_metrics()
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'all_workers': all_workers,
                'stale_workers': stale_workers,
                'metrics': metrics,
                'recovery_stats': self.get_recovery_stats()
            }
            
        except Exception as e:
            logger.error(f"Error getting worker status: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        
        finally:
            if db_session:
                db_session.close()
    
    @property
    def is_running(self) -> bool:
        """Check if recovery system is running."""
        return self._running


class WorkerRecoveryNotifier:
    """
    Notification system for worker recovery events.
    
    Provides callbacks and logging for worker failure and recovery events.
    """
    
    def __init__(self):
        """Initialize the notifier."""
        self.failure_handlers: List[Callable[[List[Dict[str, Any]]], None]] = []
        self.recovery_handlers: List[Callable[[Dict[str, int]], None]] = []
    
    def add_failure_handler(self, handler: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Add handler for worker failure events."""
        self.failure_handlers.append(handler)
    
    def add_recovery_handler(self, handler: Callable[[Dict[str, int]], None]) -> None:
        """Add handler for worker recovery events."""
        self.recovery_handlers.append(handler)
    
    def notify_worker_failures(self, failed_workers: List[Dict[str, Any]]) -> None:
        """Notify all handlers about worker failures."""
        for handler in self.failure_handlers:
            try:
                handler(failed_workers)
            except Exception as e:
                logger.error(f"Error in failure handler: {e}")
    
    def notify_worker_recovery(self, recovery_stats: Dict[str, int]) -> None:
        """Notify all handlers about worker recovery."""
        for handler in self.recovery_handlers:
            try:
                handler(recovery_stats)
            except Exception as e:
                logger.error(f"Error in recovery handler: {e}")


# Default notification handlers

def log_worker_failures(failed_workers: List[Dict[str, Any]]) -> None:
    """Default handler that logs worker failures."""
    for worker in failed_workers:
        logger.error(
            f"Worker failure detected: {worker['worker_id']} "
            f"(last heartbeat: {worker.get('last_heartbeat', 'never')}, "
            f"state: {worker.get('state', 'unknown')})"
        )


def log_worker_recovery(recovery_stats: Dict[str, int]) -> None:
    """Default handler that logs worker recovery actions."""
    logger.info(
        f"Worker recovery completed: "
        f"{recovery_stats.get('workers_marked_failed', 0)} workers failed, "
        f"{recovery_stats.get('entries_released', 0)} entries released"
    )


def create_default_recovery_system(db_session_factory: Callable[[], Session],
                                 **kwargs) -> WorkerRecoverySystem:
    """
    Create a recovery system with default notification handlers.
    
    Args:
        db_session_factory: Database session factory
        **kwargs: Additional arguments for WorkerRecoverySystem
        
    Returns:
        Configured WorkerRecoverySystem instance
    """
    recovery_system = WorkerRecoverySystem(db_session_factory, **kwargs)
    
    # Add default notification handlers
    recovery_system.add_failure_callback(log_worker_failures)
    recovery_system.add_recovery_callback(log_worker_recovery)
    
    return recovery_system