"""
Worker Maintenance System for distributed ETL processing.

This module provides comprehensive worker maintenance functionality including:
- Automatic worker failure detection and recovery
- Stale claim cleanup and resource management
- Worker performance monitoring and optimization
- Database maintenance tasks
"""

import logging
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional, Callable
from sqlalchemy.orm import Session

from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.work_queue_manager import WorkQueueManager

logger = logging.getLogger(__name__)


class WorkerMaintenanceSystem:
    """
    Comprehensive worker maintenance system for distributed ETL processing.

    Provides automatic maintenance functionality including:
    - Worker failure detection and recovery
    - Stale claim cleanup and resource management
    - Performance monitoring and optimization
    - Database maintenance tasks
    - Worker lifecycle management
    """

    def __init__(self,
                 db_session_factory: Callable[[], Session],
                 check_interval_seconds: int = 60,
                 stale_threshold_minutes: int = 5,
                 claim_timeout_minutes: int = 30,
                 enable_performance_monitoring: bool = True,
                 enable_database_maintenance: bool = True):
        """
        Initialize the maintenance system.

        Args:
            db_session_factory: Factory function to create database sessions
            check_interval_seconds: How often to perform maintenance checks
            stale_threshold_minutes: Minutes after which a worker is considered failed
            claim_timeout_minutes: Minutes after which claims are released
            enable_performance_monitoring: Enable performance monitoring and optimization
            enable_database_maintenance: Enable database maintenance tasks
        """
        self.db_session_factory = db_session_factory
        self.check_interval_seconds = check_interval_seconds
        self.stale_threshold_minutes = stale_threshold_minutes
        self.claim_timeout_minutes = claim_timeout_minutes
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_database_maintenance = enable_database_maintenance
        
        # Maintenance system state
        self._running = False
        self._maintenance_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()

        # Statistics tracking (expanded)
        self._stats = {
            'maintenance_cycles': 0,
            'workers_recovered': 0,
            'entries_released': 0,
            'performance_optimizations': 0,
            'database_maintenance_runs': 0,
            'stale_claims_cleaned': 0,
            'last_maintenance_time': None,
            'last_error': None,
            'last_performance_check': None,
            'last_database_maintenance': None
        }

        # Callback lists for notifications
        self._failure_callbacks: List[Callable[[List[Dict[str, Any]]], None]] = []
        self._recovery_callbacks: List[Callable[[Dict[str, int]], None]] = []
        self._maintenance_callbacks: List[Callable[[Dict[str, Any]], None]] = []
    
    def start(self) -> None:
        """Start the maintenance system."""
        if self._running:
            logger.warning("Worker maintenance system is already running")
            return

        logger.info(
            f"Starting worker maintenance system "
            f"(check_interval: {self.check_interval_seconds}s, "
            f"stale_threshold: {self.stale_threshold_minutes}m, "
            f"performance_monitoring: {self.enable_performance_monitoring}, "
            f"database_maintenance: {self.enable_database_maintenance})"
        )

        self._running = True
        self._shutdown_event.clear()

        # Start maintenance thread
        self._maintenance_thread = threading.Thread(
            target=self._maintenance_loop,
            name="worker-maintenance",
            daemon=True
        )
        self._maintenance_thread.start()

        logger.info("Worker maintenance system started")
    
    def stop(self, timeout: int = 10) -> None:
        """Stop the maintenance system."""
        if not self._running:
            return

        logger.info("Stopping worker maintenance system...")

        self._running = False
        self._shutdown_event.set()

        # Wait for maintenance thread to finish
        if self._maintenance_thread and self._maintenance_thread.is_alive():
            self._maintenance_thread.join(timeout=timeout)

            if self._maintenance_thread.is_alive():
                logger.warning("Maintenance thread did not stop within timeout")

        logger.info("Worker maintenance system stopped")
    
    def add_failure_callback(self, callback: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Add callback to be called when worker failures are detected."""
        self._failure_callbacks.append(callback)
    
    def add_recovery_callback(self, callback: Callable[[Dict[str, int]], None]) -> None:
        """Add callback to be called when recovery actions are taken."""
        self._recovery_callbacks.append(callback)

    def add_maintenance_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Add callback to be called when maintenance actions are taken."""
        self._maintenance_callbacks.append(callback)
    
    def _maintenance_loop(self) -> None:
        """Main maintenance loop."""
        logger.info("Worker maintenance loop started")

        while self._running and not self._shutdown_event.is_set():
            try:
                # Perform maintenance cycle
                self._perform_maintenance_cycle()

                # Wait for next check
                self._shutdown_event.wait(self.check_interval_seconds)

            except Exception as e:
                logger.error(f"Error in maintenance loop: {e}", exc_info=True)
                self._stats['last_error'] = str(e)

                # Wait before retrying
                self._shutdown_event.wait(min(self.check_interval_seconds, 30))

        logger.info("Worker maintenance loop stopped")
    
    def _perform_maintenance_cycle(self) -> None:
        """Perform a comprehensive maintenance cycle."""
        maintenance_stats = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'recovery_performed': False,
            'performance_check_performed': False,
            'database_maintenance_performed': False,
            'stale_cleanup_performed': False
        }

        try:
            # 1. Core recovery check (always performed)
            recovery_stats = self._perform_recovery_check()
            if recovery_stats.get('workers_recovered', 0) > 0:
                maintenance_stats['recovery_performed'] = True
                maintenance_stats['recovery_stats'] = recovery_stats

            # 2. Performance monitoring and optimization
            if self.enable_performance_monitoring:
                perf_stats = self._perform_performance_check()
                if perf_stats:
                    maintenance_stats['performance_check_performed'] = True
                    maintenance_stats['performance_stats'] = perf_stats
                    self._stats['performance_optimizations'] += perf_stats.get('optimizations_applied', 0)

            # 3. Database maintenance tasks
            if self.enable_database_maintenance:
                db_stats = self._perform_database_maintenance()
                if db_stats:
                    maintenance_stats['database_maintenance_performed'] = True
                    maintenance_stats['database_stats'] = db_stats
                    self._stats['database_maintenance_runs'] += 1

            # 4. Additional stale cleanup
            cleanup_stats = self._perform_extended_cleanup()
            if cleanup_stats.get('claims_cleaned', 0) > 0:
                maintenance_stats['stale_cleanup_performed'] = True
                maintenance_stats['cleanup_stats'] = cleanup_stats
                self._stats['stale_claims_cleaned'] += cleanup_stats.get('claims_cleaned', 0)

            # Update statistics
            self._stats['maintenance_cycles'] += 1
            self._stats['last_maintenance_time'] = datetime.now(timezone.utc).isoformat()

            # Notify maintenance callbacks
            for callback in self._maintenance_callbacks:
                try:
                    callback(maintenance_stats)
                except Exception as e:
                    logger.error(f"Error in maintenance callback: {e}")

        except Exception as e:
            logger.error(f"Error in maintenance cycle: {e}", exc_info=True)
            self._stats['last_error'] = str(e)

    def _perform_recovery_check(self) -> Dict[str, Any]:
        """Perform a single recovery check cycle."""
        db_session = None
        
        try:
            # Create database session
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            work_queue = WorkQueueManager(db_session)

            # Update worker timeouts (3 min -> stopped, 5 min -> error)
            timeout_stats = health_manager.update_worker_timeouts(
                stopped_threshold_minutes=3,
                error_threshold_minutes=5
            )

            # Detect stale workers
            stale_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_threshold_minutes
            )
            
            if stale_workers:
                logger.warning(f"Detected {len(stale_workers)} stale workers")
                
                # Notify failure callbacks
                for callback in self._failure_callbacks:
                    try:
                        callback(stale_workers)
                    except Exception as e:
                        logger.error(f"Error in failure callback: {e}")
                
                # Perform recovery
                recovery_stats = health_manager.recover_failed_workers(
                    stale_threshold_minutes=self.stale_threshold_minutes,
                    claim_timeout_minutes=self.claim_timeout_minutes
                )
                
                # Update statistics
                self._stats['workers_recovered'] += recovery_stats['workers_marked_failed']
                self._stats['entries_released'] += recovery_stats['entries_released']
                
                logger.info(
                    f"Recovery completed: {recovery_stats['workers_marked_failed']} workers failed, "
                    f"{recovery_stats['entries_released']} entries released"
                )
                
                # Notify recovery callbacks
                for callback in self._recovery_callbacks:
                    try:
                        callback(recovery_stats)
                    except Exception as e:
                        logger.error(f"Error in recovery callback: {e}")
            
            # Perform additional cleanup
            self._perform_additional_cleanup(work_queue)
            
            # Update statistics
            self._stats['recovery_cycles'] += 1
            self._stats['last_recovery_time'] = datetime.now(timezone.utc).isoformat()

            # Return recovery statistics
            return {
                'workers_recovered': recovery_stats.get('workers_marked_failed', 0) if 'recovery_stats' in locals() else 0,
                'entries_released': recovery_stats.get('entries_released', 0) if 'recovery_stats' in locals() else 0,
                'stale_workers_detected': len(stale_workers) if stale_workers else 0
            }

        except Exception as e:
            logger.error(f"Error performing recovery check: {e}", exc_info=True)
            self._stats['last_error'] = str(e)
            return {'workers_recovered': 0, 'entries_released': 0, 'stale_workers_detected': 0}

        finally:
            if db_session:
                db_session.close()
    
    def _perform_additional_cleanup(self, work_queue: WorkQueueManager) -> None:
        """Perform additional cleanup operations."""
        try:
            # Create a separate session for cleanup to avoid transaction conflicts
            with self.db_session_factory() as cleanup_session:
                cleanup_work_queue = WorkQueueManager(cleanup_session)

                # Clean up very old stale claims (double the normal timeout)
                extended_timeout = self.claim_timeout_minutes * 2
                old_claims_cleaned = cleanup_work_queue.cleanup_stale_claims(extended_timeout)

                if old_claims_cleaned > 0:
                    logger.info(f"Cleaned up {old_claims_cleaned} very old stale claims")

        except Exception as e:
            logger.warning(f"Error in additional cleanup: {e}")

    def _perform_performance_check(self) -> Optional[Dict[str, Any]]:
        """Perform performance monitoring and optimization."""
        try:
            db_session = self.db_session_factory()

            # Get worker performance metrics
            health_manager = WorkerHealthManager(db_session)
            worker_metrics = health_manager.get_worker_metrics()

            performance_stats = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'workers_analyzed': len(worker_metrics) if worker_metrics else 0,
                'optimizations_applied': 0,
                'recommendations': []
            }

            # Analyze performance and generate recommendations
            if worker_metrics:
                for worker_id, metrics in worker_metrics.items():
                    # Check for performance issues
                    success_rate = metrics.get('success_rate', 100)
                    avg_batch_time = metrics.get('avg_batch_time', 0)

                    if success_rate < 80:
                        performance_stats['recommendations'].append({
                            'worker_id': worker_id,
                            'issue': 'low_success_rate',
                            'value': success_rate,
                            'recommendation': 'Check worker logs for errors'
                        })

                    if avg_batch_time > 300:  # 5 minutes
                        performance_stats['recommendations'].append({
                            'worker_id': worker_id,
                            'issue': 'slow_processing',
                            'value': avg_batch_time,
                            'recommendation': 'Consider reducing batch size or optimizing processing'
                        })

            self._stats['last_performance_check'] = datetime.now(timezone.utc).isoformat()
            return performance_stats

        except Exception as e:
            logger.error(f"Error in performance check: {e}")
            return None
        finally:
            if 'db_session' in locals():
                db_session.close()

    def _perform_database_maintenance(self) -> Optional[Dict[str, Any]]:
        """Perform database maintenance tasks."""
        try:
            db_session = self.db_session_factory()

            maintenance_stats = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'tasks_performed': []
            }

            # Clean up old worker records (older than 7 days)
            from backend.app.models.models import Worker
            cutoff_date = datetime.utcnow() - timedelta(days=7)

            old_workers = db_session.query(Worker).filter(
                Worker.last_heartbeat < cutoff_date,
                Worker.status.in_(['error', 'stopped'])
            ).all()

            if old_workers:
                for worker in old_workers:
                    db_session.delete(worker)
                db_session.commit()

                maintenance_stats['tasks_performed'].append({
                    'task': 'cleanup_old_workers',
                    'count': len(old_workers)
                })

            self._stats['last_database_maintenance'] = datetime.now(timezone.utc).isoformat()
            return maintenance_stats

        except Exception as e:
            logger.error(f"Error in database maintenance: {e}")
            return None
        finally:
            if 'db_session' in locals():
                db_session.close()

    def _perform_extended_cleanup(self) -> Dict[str, Any]:
        """Perform extended cleanup operations."""
        cleanup_stats = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'claims_cleaned': 0
        }

        try:
            # Create a separate session for cleanup to avoid transaction conflicts
            db_session = self.db_session_factory()
            cleanup_work_queue = WorkQueueManager(db_session)

            # Clean up very old stale claims (double the normal timeout)
            extended_timeout = self.claim_timeout_minutes * 2
            old_claims_cleaned = cleanup_work_queue.cleanup_stale_claims(extended_timeout)

            cleanup_stats['claims_cleaned'] = old_claims_cleaned

            if old_claims_cleaned > 0:
                logger.info(f"Extended cleanup: cleaned up {old_claims_cleaned} very old stale claims")

        except Exception as e:
            logger.warning(f"Error in extended cleanup: {e}")
        finally:
            if 'db_session' in locals():
                db_session.close()

        return cleanup_stats

    def force_recovery_check(self) -> Dict[str, Any]:
        """
        Force an immediate recovery check.
        
        Returns:
            Dictionary with recovery results
        """
        if not self._running:
            raise RuntimeError("Recovery system is not running")
        
        logger.info("Forcing immediate recovery check")
        
        db_session = None
        try:
            # Create database session
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            
            # Detect and recover from failed workers
            stale_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_threshold_minutes
            )
            
            recovery_stats = health_manager.recover_failed_workers(
                stale_threshold_minutes=self.stale_threshold_minutes,
                claim_timeout_minutes=self.claim_timeout_minutes
            )
            
            # Add stale worker information
            recovery_stats['stale_workers'] = stale_workers
            recovery_stats['timestamp'] = datetime.now(timezone.utc).isoformat()
            
            logger.info(f"Forced recovery completed: {recovery_stats}")
            
            return recovery_stats
            
        except Exception as e:
            logger.error(f"Error in forced recovery check: {e}")
            raise
        
        finally:
            if db_session:
                db_session.close()
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery system statistics."""
        return {
            'running': self._running,
            'check_interval_seconds': self.check_interval_seconds,
            'stale_threshold_minutes': self.stale_threshold_minutes,
            'claim_timeout_minutes': self.claim_timeout_minutes,
            'stats': self._stats.copy()
        }
    
    def get_current_worker_status(self) -> Dict[str, Any]:
        """Get current status of all workers."""
        db_session = None
        try:
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            
            # Get all workers and their health
            all_workers = health_manager.get_all_workers_health()
            
            # Get stale workers
            stale_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_threshold_minutes
            )
            
            # Get worker metrics
            metrics = health_manager.get_worker_metrics()
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'all_workers': all_workers,
                'stale_workers': stale_workers,
                'metrics': metrics,
                'recovery_stats': self.get_recovery_stats()
            }
            
        except Exception as e:
            logger.error(f"Error getting worker status: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        
        finally:
            if db_session:
                db_session.close()
    
    @property
    def is_running(self) -> bool:
        """Check if recovery system is running."""
        return self._running


class WorkerRecoveryNotifier:
    """
    Notification system for worker recovery events.
    
    Provides callbacks and logging for worker failure and recovery events.
    """
    
    def __init__(self):
        """Initialize the notifier."""
        self.failure_handlers: List[Callable[[List[Dict[str, Any]]], None]] = []
        self.recovery_handlers: List[Callable[[Dict[str, int]], None]] = []
    
    def add_failure_handler(self, handler: Callable[[List[Dict[str, Any]]], None]) -> None:
        """Add handler for worker failure events."""
        self.failure_handlers.append(handler)
    
    def add_recovery_handler(self, handler: Callable[[Dict[str, int]], None]) -> None:
        """Add handler for worker recovery events."""
        self.recovery_handlers.append(handler)
    
    def notify_worker_failures(self, failed_workers: List[Dict[str, Any]]) -> None:
        """Notify all handlers about worker failures."""
        for handler in self.failure_handlers:
            try:
                handler(failed_workers)
            except Exception as e:
                logger.error(f"Error in failure handler: {e}")
    
    def notify_worker_recovery(self, recovery_stats: Dict[str, int]) -> None:
        """Notify all handlers about worker recovery."""
        for handler in self.recovery_handlers:
            try:
                handler(recovery_stats)
            except Exception as e:
                logger.error(f"Error in recovery handler: {e}")


# Default notification handlers

def log_worker_failures(failed_workers: List[Dict[str, Any]]) -> None:
    """Default handler that logs worker failures."""
    for worker in failed_workers:
        logger.error(
            f"Worker failure detected: {worker['worker_id']} "
            f"(last heartbeat: {worker.get('last_heartbeat', 'never')}, "
            f"state: {worker.get('state', 'unknown')})"
        )


def log_worker_recovery(recovery_stats: Dict[str, int]) -> None:
    """Default handler that logs worker recovery actions."""
    logger.info(
        f"Worker recovery completed: "
        f"{recovery_stats.get('workers_marked_failed', 0)} workers failed, "
        f"{recovery_stats.get('entries_released', 0)} entries released"
    )


def create_default_maintenance_system(db_session_factory: Callable[[], Session],
                                    **kwargs) -> WorkerMaintenanceSystem:
    """
    Create a maintenance system with default notification handlers.

    Args:
        db_session_factory: Database session factory
        **kwargs: Additional arguments for WorkerMaintenanceSystem

    Returns:
        Configured WorkerMaintenanceSystem instance
    """
    maintenance_system = WorkerMaintenanceSystem(db_session_factory, **kwargs)
    
    # Add default notification handlers
    maintenance_system.add_failure_callback(log_worker_failures)
    maintenance_system.add_recovery_callback(log_worker_recovery)

    return maintenance_system


# Backward compatibility alias
def create_default_recovery_system(db_session_factory: Callable[[], Session],
                                 **kwargs) -> WorkerMaintenanceSystem:
    """
    Create a maintenance system with default notification handlers.

    DEPRECATED: Use create_default_maintenance_system instead.
    This function is kept for backward compatibility.
    """
    return create_default_maintenance_system(db_session_factory, **kwargs)