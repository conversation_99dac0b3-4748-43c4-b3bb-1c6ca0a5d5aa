#!/usr/bin/env python3
"""
Integrated Distributed Worker CLI - Command line interface for distributed workers.

This CLI provides both parallel worker execution and comprehensive management
functionality for distributed ETL processing.
"""

import argparse
import asyncio
import json
import logging
import os
import signal
import sys
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from backend.app.distributed.parallel_worker import (
    OneThreadPerStageWorker,
    start_monolithic_style_threads
)
from backend.app.distributed.worker_config import WorkerConfig, WorkerConfigLoader
from backend.app.distributed.worker_factory import WorkerFactory
from backend.app.distributed.worker_manager import WorkerManager
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.metrics_collector import MetricsCollector
from backend.app.distributed.health_check_server import HealthCheckServer
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.logging_config import setup_distributed_logging
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.logging_config import setup_distributed_logging

logger = logging.getLogger(__name__)


def parse_stages(stage_names: List[str]) -> List[ProcessingStage]:
    """Parse stage names into ProcessingStage enums."""
    stages = []
    available_stages = {stage.value: stage for stage in ProcessingStage}
    
    for name in stage_names:
        if name in available_stages:
            stages.append(available_stages[name])
        else:
            print(f"Error: Unknown stage '{name}'")
            print(f"Available stages: {', '.join(available_stages.keys())}")
            sys.exit(1)
    
    return stages


def get_stages_from_env() -> Optional[List[ProcessingStage]]:
    """Get stages list from environment variables."""
    # Check for WORKER_STAGES_INCLUDE first (explicit inclusion)
    include_env = os.getenv('WORKER_STAGES_INCLUDE')
    if include_env:
        stage_names = [name.strip() for name in include_env.split(',') if name.strip()]
        logger.info(f"Using stages from WORKER_STAGES_INCLUDE: {stage_names}")
        return parse_stages(stage_names)

    # Check for WORKER_STAGES_EXCLUDE (exclusion from all stages)
    exclude_env = os.getenv('WORKER_STAGES_EXCLUDE')
    if exclude_env:
        exclude_names = [name.strip() for name in exclude_env.split(',') if name.strip()]
        logger.info(f"Excluding stages from WORKER_STAGES_EXCLUDE: {exclude_names}")

        # Start with all stages except download_feeds (default behavior)
        all_stages = [s for s in ProcessingStage if s != ProcessingStage.DOWNLOAD_FEEDS]

        # Remove excluded stages
        excluded_stages = set(parse_stages(exclude_names))
        filtered_stages = [s for s in all_stages if s not in excluded_stages]

        return filtered_stages

    return None


def get_stages_from_args(args) -> List[ProcessingStage]:
    """Get stages list from command line arguments and environment variables."""
    # Priority 1: Command line arguments
    if args.stages:
        stages = parse_stages(args.stages)
        logger.info(f"Using stages from command line: {[s.value for s in stages]}")
        return stages

    # Priority 2: Environment variables
    env_stages = get_stages_from_env()
    if env_stages is not None:
        return env_stages

    # Priority 3: Default behavior
    stages = [s for s in ProcessingStage if s != ProcessingStage.DOWNLOAD_FEEDS]
    logger.info(f"Using default stages (all except download_feeds): {[s.value for s in stages]}")
    return stages


def run_single_worker(args):
    """Run one thread per stage worker (matches monolithic ETL)."""
    logger.info("Starting one-thread-per-stage worker (monolithic ETL style)")

    stages = get_stages_from_args(args)
    worker = OneThreadPerStageWorker(stages)

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down worker...")
        worker.stop(timeout=30.0)
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        worker.start()

        logger.info(f"Started {len(stages)} stage threads:")
        for stage in stages:
            logger.info(f"  - {stage.value}Thread")

        # Keep the main thread alive and periodically log status
        while True:
            time.sleep(args.status_interval)

            status = worker.get_status()
            active_threads = status['active_threads']
            total_stages = status['total_stages']

            # Log summary statistics
            total_batches = sum(stats['batches_processed'] for stats in status['stage_stats'].values())
            total_errors = sum(stats['errors'] for stats in status['stage_stats'].values())

            logger.info(f"Worker {worker.worker_id}: {active_threads}/{total_stages} threads active, "
                       f"{total_batches} batches processed, {total_errors} errors")

            # Log per-stage statistics if verbose
            if args.verbose:
                for stage_name, stats in status['stage_stats'].items():
                    if stats['batches_processed'] > 0 or stats['errors'] > 0:
                        logger.info(f"  {stage_name}: {stats['batches_processed']} batches, "
                                   f"{stats['errors']} errors, last activity: {stats['last_activity']}")

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down...")
        worker.stop(timeout=30.0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        worker.stop(timeout=30.0)
        sys.exit(1)


def run_monolithic_style(args):
    """Run monolithic-style threads (one thread per stage)."""
    logger.info("Starting monolithic-style stage threads")

    stages = get_stages_from_args(args)

    # Use the convenience function that matches monolithic ETL exactly
    worker = start_monolithic_style_threads(stages)

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down all stage threads...")
        worker.stop(timeout=30.0)
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Keep the main thread alive and periodically log status
        while True:
            time.sleep(args.status_interval)

            status = worker.get_status()
            active_threads = status['active_threads']
            total_stages = status['total_stages']

            # Log summary statistics
            total_batches = sum(stats['batches_processed'] for stats in status['stage_stats'].values())
            total_errors = sum(stats['errors'] for stats in status['stage_stats'].values())

            logger.info(f"Monolithic-style threads: {active_threads}/{total_stages} active, "
                       f"{total_batches} batches processed, {total_errors} errors")

            # Log per-stage statistics if verbose
            if args.verbose:
                for stage_name, stats in status['stage_stats'].items():
                    if stats['batches_processed'] > 0 or stats['errors'] > 0:
                        logger.info(f"  {stage_name}Thread: {stats['batches_processed']} batches, "
                                   f"{stats['errors']} errors, last activity: {stats['last_activity']}")

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down...")
        worker.stop(timeout=30.0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        worker.stop(timeout=30.0)
        sys.exit(1)


def list_stages():
    """List all available processing stages."""
    print("Available processing stages:")
    for stage in ProcessingStage:
        print(f"  {stage.value}")


async def run_management_command(args):
    """Route management commands to appropriate handlers."""
    if args.command == 'list':
        await list_workers(args)
    elif args.command == 'status':
        await show_status(args)
    elif args.command == 'queue-status':
        await show_queue_status(args)
    elif args.command == 'cleanup':
        await cleanup_stale_work(args)
    elif args.command == 'manager':
        await start_manager(args)
    elif args.command == 'metrics':
        await show_metrics(args)


async def list_workers(args):
    """List all active workers."""
    try:
        from backend.app.core.database import SessionLocal
        from backend.app.models.models import Worker

        db = SessionLocal()
        try:
            # Get all workers from database
            workers = db.query(Worker).all()

            # Apply filters
            if args.filter_stage:
                workers = [w for w in workers if args.filter_stage in (w.stages or '')]
            if args.filter_status:
                workers = [w for w in workers if w.status == args.filter_status]

            if args.format == 'json':
                worker_data = []
                for worker in workers:
                    worker_data.append({
                        'worker_id': worker.worker_id,
                        'status': worker.status,
                        'stages': worker.stages,
                        'last_heartbeat': worker.last_heartbeat.isoformat() if worker.last_heartbeat else None,
                        'entries_processed': worker.entries_processed,
                        'entries_failed': worker.entries_failed
                    })
                print(json.dumps(worker_data, indent=2))
            else:
                # Table format
                print(f"{'Worker ID':<20} {'Status':<10} {'Stages':<30} {'Last Heartbeat':<20} {'Processed':<10} {'Failed':<8}")
                print("-" * 108)
                for worker in workers:
                    last_hb = worker.last_heartbeat.strftime('%Y-%m-%d %H:%M:%S') if worker.last_heartbeat else 'Never'
                    stages_str = (worker.stages or '')[:28] + ('...' if len(worker.stages or '') > 28 else '')
                    print(f"{worker.worker_id:<20} {worker.status:<10} {stages_str:<30} {last_hb:<20} {worker.entries_processed:<10} {worker.entries_failed:<8}")

                print(f"\nTotal workers: {len(workers)}")
        finally:
            db.close()

    except Exception as e:
        print(f"Error listing workers: {e}")
        sys.exit(1)


async def show_status(args):
    """Show worker status and metrics."""
    try:
        from backend.app.core.database import SessionLocal

        db_session = SessionLocal()
        try:
            health_manager = WorkerHealthManager(db_session)

            if args.worker_id:
                # Show specific worker status
                worker_health = health_manager.get_worker_health(args.worker_id)
                if not worker_health:
                    print(f"Worker {args.worker_id} not found")
                    return

                print(f"Worker: {args.worker_id}")
                print(f"Status: {worker_health.get('status', 'Unknown')}")
                print(f"Last Heartbeat: {worker_health.get('last_heartbeat', 'Never')}")
                print(f"Entries Processed: {worker_health.get('entries_processed', 0)}")
                print(f"Entries Failed: {worker_health.get('entries_failed', 0)}")

                if args.detailed:
                    stats = worker_health.get('stats', {})
                    print(f"Batches Processed: {stats.get('batches_processed', 0)}")
                    print(f"Total Processing Time: {stats.get('total_processing_time', 0):.2f}s")
                    print(f"Average Batch Time: {stats.get('avg_batch_time', 0):.2f}s")
            else:
                # Show all workers status
                all_workers = health_manager.get_all_workers_health()

                print(f"{'Worker ID':<20} {'Status':<10} {'Last Heartbeat':<20} {'Processed':<10} {'Failed':<8}")
                print("-" * 78)

                for worker in all_workers:
                    if worker:
                        last_hb = worker.get('last_heartbeat', 'Never')
                        if last_hb != 'Never' and isinstance(last_hb, str):
                            try:
                                last_hb = datetime.fromisoformat(last_hb.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
                            except:
                                pass

                        print(f"{worker.get('worker_id', 'Unknown'):<20} "
                              f"{worker.get('status', 'Unknown'):<10} "
                              f"{last_hb:<20} "
                              f"{worker.get('entries_processed', 0):<10} "
                              f"{worker.get('entries_failed', 0):<8}")

                print(f"\nTotal workers: {len([w for w in all_workers if w])}")
        finally:
            db_session.close()

    except Exception as e:
        print(f"Error showing status: {e}")
        sys.exit(1)


async def show_queue_status(args):
    """Show work queue status."""
    try:
        from backend.app.core.database import SessionLocal
        from backend.app.models.models import Entry
        from backend.app.distributed.processing_stage import ProcessingStage

        db = SessionLocal()
        try:
            work_queue = WorkQueueManager(db)

            # Get queue status for each stage
            queue_data = []
            stages_to_check = [ProcessingStage(args.stage)] if args.stage else list(ProcessingStage)

            for stage in stages_to_check:
                try:
                    # Count entries in different states for this stage
                    total_entries = db.query(Entry).count()

                    # Count entries that need this stage
                    pending_count = work_queue.count_pending_entries(stage)

                    # Count entries currently claimed for this stage
                    claimed_count = db.query(Entry).filter(
                        Entry.claimed_by.isnot(None),
                        Entry.processing_status == stage.value
                    ).count()

                    queue_data.append({
                        'stage': stage.value,
                        'total_entries': total_entries,
                        'pending': pending_count,
                        'claimed': claimed_count
                    })
                except Exception as e:
                    print(f"Error getting status for stage {stage.value}: {e}")

            if args.format == 'json':
                print(json.dumps(queue_data, indent=2))
            else:
                # Table format
                print(f"{'Stage':<25} {'Total':<10} {'Pending':<10} {'Claimed':<10}")
                print("-" * 65)
                for data in queue_data:
                    print(f"{data['stage']:<25} {data['total_entries']:<10} {data['pending']:<10} {data['claimed']:<10}")
        finally:
            db.close()

    except Exception as e:
        print(f"Error showing queue status: {e}")
        sys.exit(1)


async def cleanup_stale_work(args):
    """Clean up stale work claims."""
    try:
        from backend.app.core.database import SessionLocal
        from backend.app.models.models import Entry

        # Calculate cutoff time
        cutoff_time = datetime.utcnow() - timedelta(minutes=args.timeout)

        db = SessionLocal()
        try:
            # Find stale claims (entries claimed but not completed or failed)
            stale_entries = db.query(Entry).filter(
                Entry.claimed_by.isnot(None),
                Entry.claimed_at < cutoff_time
            ).all()

            if args.dry_run:
                print(f"Dry run: Finding stale claims older than {args.timeout} minutes")
                print(f"Found {len(stale_entries)} stale claims:")
                for entry in stale_entries:
                    print(f"  Entry: {entry.entry_id}, Worker: {entry.claimed_by}, "
                          f"Claimed: {entry.claimed_at.isoformat() if entry.claimed_at else 'unknown'}")
            else:
                print(f"Cleaning up {len(stale_entries)} stale claims older than {args.timeout} minutes")

                # Release stale claims
                cleaned_count = 0
                for entry in stale_entries:
                    entry.claimed_by = None
                    entry.claimed_at = None
                    cleaned_count += 1

                db.commit()
                print(f"Successfully cleaned up {cleaned_count} stale claims")
        finally:
            db.close()

    except Exception as e:
        print(f"Error cleaning up stale work: {e}")
        sys.exit(1)


async def start_manager(args):
    """Start worker manager with health endpoints."""
    try:
        from backend.app.core.database import SessionLocal

        print(f"Starting worker manager on port {args.port} with {args.workers} workers...")

        # Create worker manager
        manager = WorkerManager(
            max_workers=args.workers,
            auto_scaling_enabled=True,
            db_session_factory=SessionLocal
        )

        # Create health check server
        def manager_health_provider():
            return {
                'status': 'healthy',
                'workers': len(manager.workers),
                'timestamp': datetime.utcnow().isoformat()
            }

        health_server = HealthCheckServer(args.port, manager_health_provider)
        health_server.start()

        try:
            manager.start()
            print(f"Worker manager started with health endpoint at http://localhost:{args.port}/health")

            # Keep the manager running until interrupted
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("Received interrupt signal, shutting down manager...")
            health_server.stop()
            manager.stop()

    except Exception as e:
        print(f"Error starting manager: {e}")
        sys.exit(1)


async def show_metrics(args):
    """Show detailed worker metrics."""
    try:
        from backend.app.core.database import SessionLocal

        db_session = SessionLocal()
        try:
            # Create metrics collector
            metrics_collector = MetricsCollector(db_session)

            # Get system metrics
            system_metrics = metrics_collector.collect_system_metrics()

            # Get worker metrics
            worker_metrics = metrics_collector.get_worker_metrics()

            if args.format == 'json':
                metrics_data = {
                    'system': system_metrics,
                    'workers': worker_metrics,
                    'timestamp': datetime.utcnow().isoformat()
                }
                print(json.dumps(metrics_data, indent=2))
            else:
                # Table format
                print("=== System Metrics ===")
                print(f"CPU Usage: {system_metrics.get('cpu_percent', 0):.1f}%")
                print(f"Memory Usage: {system_metrics.get('memory_percent', 0):.1f}%")
                print(f"Disk Usage: {system_metrics.get('disk_percent', 0):.1f}%")

                print("\n=== Worker Metrics ===")
                if worker_metrics:
                    print(f"{'Worker ID':<20} {'Entries/sec':<12} {'Success Rate':<12} {'Avg Batch Time':<15}")
                    print("-" * 69)
                    for worker_id, metrics in worker_metrics.items():
                        entries_per_sec = metrics.get('entries_per_second', 0)
                        success_rate = metrics.get('success_rate', 0)
                        avg_batch_time = metrics.get('avg_batch_time', 0)
                        print(f"{worker_id:<20} {entries_per_sec:<12.2f} {success_rate:<12.1f}% {avg_batch_time:<15.2f}s")
                else:
                    print("No worker metrics available")
        finally:
            db_session.close()

    except Exception as e:
        print(f"Error showing metrics: {e}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Integrated Distributed Worker CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run one thread per stage (matches monolithic ETL) - PRIMARY USE CASE
  python -m backend.app.distributed.cli.parallel_worker_cli monolithic

  # Run specific stages only
  python -m backend.app.distributed.cli.parallel_worker_cli monolithic --stages download_full_text llm_analysis

  # Run single worker (for testing)
  python -m backend.app.distributed.cli.parallel_worker_cli single --stages download_full_text

  # Management commands
  python -m backend.app.distributed.cli.parallel_worker_cli list
  python -m backend.app.distributed.cli.parallel_worker_cli status
  python -m backend.app.distributed.cli.parallel_worker_cli queue-status
  python -m backend.app.distributed.cli.parallel_worker_cli cleanup --timeout 60
  python -m backend.app.distributed.cli.parallel_worker_cli manager --port 8080

  # Use environment variables to exclude stages
  WORKER_STAGES_EXCLUDE="translate_text,generate_rss_feed" python -m backend.app.distributed.cli.parallel_worker_cli monolithic

Environment Variables:
  WORKER_STAGES_INCLUDE  Comma-separated list of stages to include (overrides default)
  WORKER_STAGES_EXCLUDE  Comma-separated list of stages to exclude from default set

  Note: Command line --stages takes priority over environment variables.
        WORKER_STAGES_INCLUDE takes priority over WORKER_STAGES_EXCLUDE.
        """
    )
    
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Single worker command
    single_parser = subparsers.add_parser('single', help='Run a single parallel worker')
    single_parser.add_argument('--stages', nargs='+',
                              help='Stages to process (overrides env vars; default: all except download_feeds)')
    single_parser.add_argument('--batch-size', type=int, default=10,
                              help='Batch size for processing (default: 10)')
    single_parser.add_argument('--max-retries', type=int, default=3,
                              help='Maximum retries for failed entries (default: 3)')
    single_parser.add_argument('--processing-delay', type=float, default=1.0,
                              help='Delay between successful batches in seconds (default: 1.0)')
    single_parser.add_argument('--no-work-delay', type=float, default=5.0,
                              help='Delay when no work available in seconds (default: 5.0)')
    single_parser.add_argument('--heartbeat-interval', type=float, default=30.0,
                              help='Heartbeat interval in seconds (default: 30.0)')
    single_parser.add_argument('--status-interval', type=float, default=60.0,
                              help='Status logging interval in seconds (default: 60.0)')

    # Monolithic style command
    monolithic_parser = subparsers.add_parser('monolithic', help='Run monolithic-style threads (one per stage)')
    monolithic_parser.add_argument('--stages', nargs='+',
                                  help='Stages to process (overrides env vars; default: all except download_feeds)')
    monolithic_parser.add_argument('--status-interval', type=float, default=60.0,
                                  help='Status logging interval in seconds (default: 60.0)')
    
    # List stages command
    subparsers.add_parser('list-stages', help='List all available processing stages')

    # Management commands (integrated from worker_cli.py)
    # List workers command
    list_parser = subparsers.add_parser('list', help='List all active workers')
    list_parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')
    list_parser.add_argument('--filter-stage', help='Filter by processing stage')
    list_parser.add_argument('--filter-status', help='Filter by worker status')

    # Worker status command
    status_parser = subparsers.add_parser('status', help='Show worker status and metrics')
    status_parser.add_argument('--worker-id', help='Specific worker ID (optional)')
    status_parser.add_argument('--detailed', action='store_true', help='Show detailed metrics')

    # Queue status command
    queue_parser = subparsers.add_parser('queue-status', help='Show work queue status')
    queue_parser.add_argument('--stage', help='Filter by processing stage')
    queue_parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')

    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up stale work claims')
    cleanup_parser.add_argument('--timeout', type=int, default=60, help='Stale timeout in minutes')
    cleanup_parser.add_argument('--dry-run', action='store_true', help='Show what would be cleaned up')

    # Manager command
    manager_parser = subparsers.add_parser('manager', help='Start worker manager with health endpoints')
    manager_parser.add_argument('--port', type=int, default=8080, help='Manager port')
    manager_parser.add_argument('--workers', type=int, default=5, help='Number of workers to manage')

    # Metrics command
    metrics_parser = subparsers.add_parser('metrics', help='Show detailed worker metrics')
    metrics_parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    # Setup logging
    setup_distributed_logging(
        log_level=args.log_level,
        worker_id="integrated-cli"
    )

    # Execute command - run async commands with asyncio
    if args.command in ['list', 'status', 'queue-status', 'cleanup', 'manager', 'metrics']:
        asyncio.run(run_management_command(args))
    elif args.command == 'single':
        run_single_worker(args)
    elif args.command == 'monolithic':
        run_monolithic_style(args)
    elif args.command == 'list-stages':
        list_stages()


if __name__ == '__main__':
    main()
