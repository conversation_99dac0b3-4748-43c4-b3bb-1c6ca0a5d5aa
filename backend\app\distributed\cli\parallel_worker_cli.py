#!/usr/bin/env python3
"""
Parallel Worker CLI - Command line interface for parallel distributed workers.

This CLI allows you to start and manage parallel workers that process multiple
ETL stages simultaneously in separate threads.
"""

import argparse
import logging
import os
import signal
import sys
import time
from typing import List, Optional

from backend.app.distributed.parallel_worker import (
    OneThreadPerStageWorker,
    start_monolithic_style_threads
)
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.logging_config import setup_distributed_logging

logger = logging.getLogger(__name__)


def parse_stages(stage_names: List[str]) -> List[ProcessingStage]:
    """Parse stage names into ProcessingStage enums."""
    stages = []
    available_stages = {stage.value: stage for stage in ProcessingStage}
    
    for name in stage_names:
        if name in available_stages:
            stages.append(available_stages[name])
        else:
            print(f"Error: Unknown stage '{name}'")
            print(f"Available stages: {', '.join(available_stages.keys())}")
            sys.exit(1)
    
    return stages


def get_stages_from_env() -> Optional[List[ProcessingStage]]:
    """Get stages list from environment variables."""
    # Check for WORKER_STAGES_INCLUDE first (explicit inclusion)
    include_env = os.getenv('WORKER_STAGES_INCLUDE')
    if include_env:
        stage_names = [name.strip() for name in include_env.split(',') if name.strip()]
        logger.info(f"Using stages from WORKER_STAGES_INCLUDE: {stage_names}")
        return parse_stages(stage_names)

    # Check for WORKER_STAGES_EXCLUDE (exclusion from all stages)
    exclude_env = os.getenv('WORKER_STAGES_EXCLUDE')
    if exclude_env:
        exclude_names = [name.strip() for name in exclude_env.split(',') if name.strip()]
        logger.info(f"Excluding stages from WORKER_STAGES_EXCLUDE: {exclude_names}")

        # Start with all stages except download_feeds (default behavior)
        all_stages = [s for s in ProcessingStage if s != ProcessingStage.DOWNLOAD_FEEDS]

        # Remove excluded stages
        excluded_stages = set(parse_stages(exclude_names))
        filtered_stages = [s for s in all_stages if s not in excluded_stages]

        return filtered_stages

    return None


def get_stages_from_args(args) -> List[ProcessingStage]:
    """Get stages list from command line arguments and environment variables."""
    # Priority 1: Command line arguments
    if args.stages:
        stages = parse_stages(args.stages)
        logger.info(f"Using stages from command line: {[s.value for s in stages]}")
        return stages

    # Priority 2: Environment variables
    env_stages = get_stages_from_env()
    if env_stages is not None:
        return env_stages

    # Priority 3: Default behavior
    stages = [s for s in ProcessingStage if s != ProcessingStage.DOWNLOAD_FEEDS]
    logger.info(f"Using default stages (all except download_feeds): {[s.value for s in stages]}")
    return stages


def run_single_worker(args):
    """Run one thread per stage worker (matches monolithic ETL)."""
    logger.info("Starting one-thread-per-stage worker (monolithic ETL style)")

    stages = get_stages_from_args(args)
    worker = OneThreadPerStageWorker(stages)

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down worker...")
        worker.stop(timeout=30.0)
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        worker.start()

        logger.info(f"Started {len(stages)} stage threads:")
        for stage in stages:
            logger.info(f"  - {stage.value}Thread")

        # Keep the main thread alive and periodically log status
        while True:
            time.sleep(args.status_interval)

            status = worker.get_status()
            active_threads = status['active_threads']
            total_stages = status['total_stages']

            # Log summary statistics
            total_batches = sum(stats['batches_processed'] for stats in status['stage_stats'].values())
            total_errors = sum(stats['errors'] for stats in status['stage_stats'].values())

            logger.info(f"Worker {worker.worker_id}: {active_threads}/{total_stages} threads active, "
                       f"{total_batches} batches processed, {total_errors} errors")

            # Log per-stage statistics if verbose
            if args.verbose:
                for stage_name, stats in status['stage_stats'].items():
                    if stats['batches_processed'] > 0 or stats['errors'] > 0:
                        logger.info(f"  {stage_name}: {stats['batches_processed']} batches, "
                                   f"{stats['errors']} errors, last activity: {stats['last_activity']}")

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down...")
        worker.stop(timeout=30.0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        worker.stop(timeout=30.0)
        sys.exit(1)


def run_monolithic_style(args):
    """Run monolithic-style threads (one thread per stage)."""
    logger.info("Starting monolithic-style stage threads")

    stages = get_stages_from_args(args)

    # Use the convenience function that matches monolithic ETL exactly
    worker = start_monolithic_style_threads(stages)

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down all stage threads...")
        worker.stop(timeout=30.0)
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Keep the main thread alive and periodically log status
        while True:
            time.sleep(args.status_interval)

            status = worker.get_status()
            active_threads = status['active_threads']
            total_stages = status['total_stages']

            # Log summary statistics
            total_batches = sum(stats['batches_processed'] for stats in status['stage_stats'].values())
            total_errors = sum(stats['errors'] for stats in status['stage_stats'].values())

            logger.info(f"Monolithic-style threads: {active_threads}/{total_stages} active, "
                       f"{total_batches} batches processed, {total_errors} errors")

            # Log per-stage statistics if verbose
            if args.verbose:
                for stage_name, stats in status['stage_stats'].items():
                    if stats['batches_processed'] > 0 or stats['errors'] > 0:
                        logger.info(f"  {stage_name}Thread: {stats['batches_processed']} batches, "
                                   f"{stats['errors']} errors, last activity: {stats['last_activity']}")

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down...")
        worker.stop(timeout=30.0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        worker.stop(timeout=30.0)
        sys.exit(1)


def list_stages():
    """List all available processing stages."""
    print("Available processing stages:")
    for stage in ProcessingStage:
        print(f"  {stage.value}")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Parallel Distributed Worker CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run one thread per stage (matches monolithic ETL)
  python -m backend.app.distributed.cli.parallel_worker_cli monolithic

  # Run specific stages only
  python -m backend.app.distributed.cli.parallel_worker_cli monolithic --stages download_full_text llm_analysis

  # Run single worker (for testing)
  python -m backend.app.distributed.cli.parallel_worker_cli single --stages download_full_text

  # Use environment variables to exclude stages
  WORKER_STAGES_EXCLUDE="translate_text,generate_rss_feed" python -m backend.app.distributed.cli.parallel_worker_cli monolithic

  # Use environment variables to include only specific stages
  WORKER_STAGES_INCLUDE="download_full_text,llm_analysis" python -m backend.app.distributed.cli.parallel_worker_cli monolithic

  # List available stages
  python -m backend.app.distributed.cli.parallel_worker_cli list-stages

Environment Variables:
  WORKER_STAGES_INCLUDE  Comma-separated list of stages to include (overrides default)
  WORKER_STAGES_EXCLUDE  Comma-separated list of stages to exclude from default set

  Note: Command line --stages takes priority over environment variables.
        WORKER_STAGES_INCLUDE takes priority over WORKER_STAGES_EXCLUDE.
        """
    )
    
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Single worker command
    single_parser = subparsers.add_parser('single', help='Run a single parallel worker')
    single_parser.add_argument('--stages', nargs='+',
                              help='Stages to process (overrides env vars; default: all except download_feeds)')
    single_parser.add_argument('--batch-size', type=int, default=10,
                              help='Batch size for processing (default: 10)')
    single_parser.add_argument('--max-retries', type=int, default=3,
                              help='Maximum retries for failed entries (default: 3)')
    single_parser.add_argument('--processing-delay', type=float, default=1.0,
                              help='Delay between successful batches in seconds (default: 1.0)')
    single_parser.add_argument('--no-work-delay', type=float, default=5.0,
                              help='Delay when no work available in seconds (default: 5.0)')
    single_parser.add_argument('--heartbeat-interval', type=float, default=30.0,
                              help='Heartbeat interval in seconds (default: 30.0)')
    single_parser.add_argument('--status-interval', type=float, default=60.0,
                              help='Status logging interval in seconds (default: 60.0)')

    # Monolithic style command
    monolithic_parser = subparsers.add_parser('monolithic', help='Run monolithic-style threads (one per stage)')
    monolithic_parser.add_argument('--stages', nargs='+',
                                  help='Stages to process (overrides env vars; default: all except download_feeds)')
    monolithic_parser.add_argument('--status-interval', type=float, default=60.0,
                                  help='Status logging interval in seconds (default: 60.0)')
    
    # List stages command
    subparsers.add_parser('list-stages', help='List all available processing stages')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Setup logging
    setup_distributed_logging(
        log_level=args.log_level,
        worker_id="parallel-cli"
    )
    
    # Execute command
    if args.command == 'single':
        run_single_worker(args)
    elif args.command == 'monolithic':
        run_monolithic_style(args)
    elif args.command == 'list-stages':
        list_stages()


if __name__ == '__main__':
    main()
