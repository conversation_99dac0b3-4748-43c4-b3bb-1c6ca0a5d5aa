"""
Image Prompt worker for distributed ETL processing.

This module provides the ImagePromptWorker class that handles generating
creative image prompts for news entries using LLM APIs in batch processing mode.
"""

import logging
import json
import requests
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from jsonschema import validate, ValidationError

from backend.app.distributed.distributed_worker import Distri<PERSON>Worker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class ImagePromptWorker(DistributedWorker):
    """
    Worker specialized in generating creative image prompts for news entries using LLM APIs.
    
    Adapts the existing generate_image_prompts logic for batch processing with
    proper rate limiting for LLM prompt generation and batch optimization.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the image prompt worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.GENERATE_IMAGE_PROMPTS not in config.stages:
            raise ValueError("ImagePromptWorker requires GENERATE_IMAGE_PROMPTS stage")
        
        # Configuration for image prompt generation
        self.sentiment_threshold = config.get_stage_config(
            ProcessingStage.GENERATE_IMAGE_PROMPTS, 'sentiment_threshold', 0.5
        )
        self.ad_threshold = config.get_stage_config(
            ProcessingStage.GENERATE_IMAGE_PROMPTS, 'ad_threshold', 0.5
        )
        self.max_input_length = config.get_stage_config(
            ProcessingStage.GENERATE_IMAGE_PROMPTS, 'max_input_length', 4000
        )
        self.max_tokens = config.get_stage_config(
            ProcessingStage.GENERATE_IMAGE_PROMPTS, 'max_tokens', 200
        )
        self.temperature = config.get_stage_config(
            ProcessingStage.GENERATE_IMAGE_PROMPTS, 'temperature', 0.7
        )

        
        # LLM providers configuration
        self._providers = config.get_stage_config(
            ProcessingStage.GENERATE_IMAGE_PROMPTS, 'llm_providers', []
        )
        
        # LLM prompt and schema for image prompt generation
        self._llm_prompt = (
            "What would be an adequate picture to accompany the given news article? "
            "Choose one and describe it purely as a list of at most 10 keywords. "
            "The keywords must be English. "
            "Avoid specific names, use generic/functional descriptions. "
            "The keywords must be separated by comma. "
        )
        
        self._llm_schema = {
            "type": "object",
            "properties": {
                "keywords": {
                    "type": "string",
                    "description": "comma-separated English keywords"
                }
            },
            "required": ["keywords"]
        }
        
        logger.info(
            f"Initialized ImagePromptWorker {self.worker_id} "
            f"(sentiment_threshold: {self.sentiment_threshold}, "
            f"ad_threshold: {self.ad_threshold}, "
            f"max_tokens: {self.max_tokens}, "
            f"temperature: {self.temperature}, "
            f"providers: {len(self._providers)})"
        )
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for image prompt generation.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be GENERATE_IMAGE_PROMPTS)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.GENERATE_IMAGE_PROMPTS:
            logger.error(f"ImagePromptWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for image prompt generation")
        
        # Filter entries that have required fields and meet criteria
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.full_text:
                logger.warning(
                    f"Entry {entry.entry_id} missing required full_text field"
                )
                results[entry.entry_id] = False
            elif entry.image_prompt:
                logger.debug(
                    f"Entry {entry.entry_id} already has image prompt, skipping"
                )
                results[entry.entry_id] = True  # Already has prompt, consider success
            elif hasattr(entry, 'llm_positive') and entry.llm_positive is not None and entry.llm_positive < self.sentiment_threshold:
                logger.debug(
                    f"Entry {entry.entry_id} sentiment score {entry.llm_positive} below threshold {self.sentiment_threshold}, skipping"
                )
                results[entry.entry_id] = True  # Doesn't meet criteria, but not an error
            elif hasattr(entry, 'llm_is_ad') and entry.llm_is_ad is not None and entry.llm_is_ad >= self.ad_threshold:
                logger.debug(
                    f"Entry {entry.entry_id} ad score {entry.llm_is_ad} above threshold {self.ad_threshold}, skipping"
                )
                results[entry.entry_id] = True  # Doesn't meet criteria, but not an error
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.info("No valid entries to process in batch")
            return results
        
        # Process valid entries individually
        batch_results = self._process_entries(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed image prompt generation batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_entries(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries for image prompt generation.

        Args:
            entries: List of valid entries to process

        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        for entry in entries:
            try:
                # Check rate limits before making call
                if not self._can_make_llm_call():
                    logger.warning(f"Rate limit reached, deferring entry {entry.entry_id}")
                    results[entry.entry_id] = False
                    continue
                
                # Prepare input text with length limit
                input_text = f"Title: {entry.title or ''}\nArticle: {entry.full_text or ''}"
                if len(input_text) > self.max_input_length:
                    input_text = input_text[:self.max_input_length]
                    logger.debug(f"Truncated input text for entry {entry.entry_id} to {self.max_input_length} characters")
                
                # Make LLM call
                logger.debug(f"Generating image prompt for entry {entry.entry_id}")
                data, provider, model = self._call_llm(
                    prompt=self._llm_prompt,
                    schema=self._llm_schema,
                    input_text=input_text,
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )
                
                # Extract keywords from response
                keywords = data.get('keywords', '').strip()
                if not keywords:
                    logger.warning(f"Empty keywords generated for entry {entry.entry_id}")
                    results[entry.entry_id] = False
                    continue
                
                # Validate that keywords contain commas (multiple keywords)
                if ',' not in keywords:
                    logger.debug(f"Single keyword generated for entry {entry.entry_id}: {keywords}")
                    # Still valid, but log for monitoring
                
                # Update database
                success = self._update_entry_image_prompt(entry.entry_id, keywords)
                results[entry.entry_id] = success
                
                if success:
                    logger.debug(
                        f"Generated image prompt for entry {entry.entry_id}: "
                        f"{keywords[:100]}{'...' if len(keywords) > 100 else ''}"
                    )
                else:
                    logger.warning(f"Failed to update database for entry {entry.entry_id}")
            
            except (requests.RequestException, json.JSONDecodeError, ValidationError) as e:
                logger.error(f"LLM error for entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
            except Exception as e:
                logger.error(f"Unexpected error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _can_make_llm_call(self) -> bool:
        """
        Check if we can make an LLM call based on rate limits.
        
        Returns:
            True if calls can be made, False if rate limited
        """
        # Simple check - in a real implementation, this would check
        # rate limits for all providers and estimate capacity
        return True
    
    def _call_llm(self, prompt: str, schema: dict, input_text: str, 
                  max_tokens: int = 200, temperature: float = 0.7, 
                  top_p: float = 0.95) -> Tuple[dict, str, str]:
        """
        Make an LLM API call with fallback logic.
        
        Args:
            prompt: System prompt for the LLM
            schema: JSON schema for response validation
            input_text: User input text
            max_tokens: Maximum tokens in response
            temperature: Temperature for response generation
            top_p: Top-p parameter for response generation
            
        Returns:
            Tuple of (response_data, provider_name, model_name)
            
        Raises:
            RuntimeError: If all providers fail
        """
        for provider_cfg in self._providers:
            provider_name = provider_cfg.get("provider")
            model = provider_cfg.get("model")
            
            try:
                response, model_used = self._call_llm_provider(
                    provider_cfg, prompt, schema, input_text, 
                    max_tokens, temperature, top_p
                )
                return response, provider_name, model_used
            
            except Exception as e:
                logger.warning(f"{provider_name} LLM failed ({model}): {e}")
                continue
        
        raise RuntimeError("All LLM providers failed")
    
    def _call_llm_provider(self, provider_cfg: dict, prompt: str, schema: dict, 
                          input_text: str, max_tokens: int = 200, 
                          temperature: float = 0.7, top_p: float = 0.95) -> Tuple[dict, str]:
        """
        Make an LLM API call to a specific provider.
        
        Centralized function for making LLM API calls to various providers.
        Adapted from news_aggregator.py implementation.
        
        Args:
            provider_cfg: Dict with keys 'provider' (required), optional 'model', optional 'limit' (calls/hour)
            prompt: System prompt for the LLM
            schema: JSON schema for response validation
            input_text: User input text
            max_tokens: Maximum tokens in response
            temperature: Temperature for response generation
            top_p: Top-p parameter for response generation
            
        Returns:
            Tuple of (validated_json_response, model_name)
            
        Raises:
            requests.RequestException: If API call fails
            json.JSONDecodeError: If response is not valid JSON
            ValidationError: If response doesn't match schema
        """
        provider = provider_cfg.get("provider")
        model = provider_cfg.get("model", "default")
        
        if provider == "openai":
            return self._call_openai_provider(provider_cfg, prompt, schema, input_text, max_tokens, temperature, top_p)
        elif provider == "anthropic":
            return self._call_anthropic_provider(provider_cfg, prompt, schema, input_text, max_tokens, temperature, top_p)
        elif provider == "local":
            return self._call_local_provider(provider_cfg, prompt, schema, input_text, max_tokens, temperature, top_p)
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")
    
    def _call_openai_provider(self, provider_cfg: dict, prompt: str, schema: dict, 
                             input_text: str, max_tokens: int, temperature: float, 
                             top_p: float) -> Tuple[dict, str]:
        """Call OpenAI API."""
        api_key = provider_cfg.get("api_key")
        model = provider_cfg.get("model", "gpt-3.5-turbo")
        base_url = provider_cfg.get("base_url", "https://api.openai.com/v1")
        
        if not api_key:
            raise ValueError("OpenAI API key not provided")
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": prompt},
                {"role": "user", "content": input_text}
            ],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "response_format": {"type": "json_object"}
        }
        
        response = requests.post(f"{base_url}/chat/completions", headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        response_data = response.json()
        content = response_data["choices"][0]["message"]["content"]
        data = json.loads(content)
        
        # Validate against schema
        validate(instance=data, schema=schema)
        
        return data, model
    
    def _call_anthropic_provider(self, provider_cfg: dict, prompt: str, schema: dict, 
                                input_text: str, max_tokens: int, temperature: float, 
                                top_p: float) -> Tuple[dict, str]:
        """Call Anthropic API."""
        api_key = provider_cfg.get("api_key")
        model = provider_cfg.get("model", "claude-3-haiku-20240307")
        base_url = provider_cfg.get("base_url", "https://api.anthropic.com")
        
        if not api_key:
            raise ValueError("Anthropic API key not provided")
        
        headers = {
            "x-api-key": api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        # Format prompt for Anthropic
        formatted_prompt = f"{prompt}\n\nPlease respond with valid JSON matching this schema: {json.dumps(schema)}\n\nInput: {input_text}"
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "messages": [
                {"role": "user", "content": formatted_prompt}
            ]
        }
        
        response = requests.post(f"{base_url}/v1/messages", headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        response_data = response.json()
        content = response_data["content"][0]["text"]
        data = json.loads(content)
        
        # Validate against schema
        validate(instance=data, schema=schema)
        
        return data, model
    
    def _call_local_provider(self, provider_cfg: dict, prompt: str, schema: dict, 
                            input_text: str, max_tokens: int, temperature: float, 
                            top_p: float) -> Tuple[dict, str]:
        """Call local LLM API (e.g., LM Studio, Ollama)."""
        base_url = provider_cfg.get("base_url", "http://127.0.0.1:1234/v1")
        model = provider_cfg.get("model", "local-model")
        
        headers = {"Content-Type": "application/json"}
        
        # Format prompt to encourage JSON response
        formatted_prompt = f"{prompt}\n\nPlease respond with valid JSON matching this schema: {json.dumps(schema)}"
        
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": formatted_prompt},
                {"role": "user", "content": input_text}
            ],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p
        }
        
        response = requests.post(f"{base_url}/chat/completions", headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        
        response_data = response.json()
        content = response_data["choices"][0]["message"]["content"]
        
        # Try to extract JSON from response (local models might include extra text)
        try:
            data = json.loads(content)
        except json.JSONDecodeError:
            # Try to find JSON in the response
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
            else:
                raise json.JSONDecodeError("No valid JSON found in response", content, 0)
        
        # Validate against schema
        validate(instance=data, schema=schema)
        
        return data, model
    
    def _update_entry_image_prompt(self, entry_id: str, image_prompt: str) -> bool:
        """
        Update the entry's image prompt in the database.
        
        Args:
            entry_id: ID of the entry to update
            image_prompt: Generated image prompt keywords
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Update image prompt field
                db_session.query(Entry).filter_by(entry_id=entry_id).update({
                    "image_prompt": image_prompt
                })
                db_session.commit()
                
                logger.debug(f"Updated image prompt for entry {entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry_id}: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to image prompt worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add image prompt worker specific metrics
        health.update({
            'worker_type': 'ImagePromptWorker',
            'sentiment_threshold': self.sentiment_threshold,
            'ad_threshold': self.ad_threshold,
            'max_input_length': self.max_input_length,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'batch_optimize_llm': self.batch_optimize_llm,
            'llm_providers_count': len(self._providers),
            'supported_stages': [ProcessingStage.GENERATE_IMAGE_PROMPTS.value]
        })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up resources when worker shuts down."""
        logger.info("Cleaning up image prompt worker resources")
        
        # No specific resources to clean up for this worker
        logger.info("Image prompt worker resources cleaned up")